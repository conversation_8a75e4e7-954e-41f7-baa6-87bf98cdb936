use anyhow::Result;
use api_version::KvFormat;
use engine_traits::CF_DEFAULT;
use kvproto::kvrpcpb::{ContentType, Context, RedisRequest, RedisResponse};
use tikv_kv::Engine;
use tikv_util::future::paired_future_callback;

use crate::{
    redis::{
        error::RedisError, meta::MetaData, parse_expire_flags, parser::CommandParser, CmdAttr,
        RedisType,
    },
    storage::{lock_manager::LockManager, Storage},
};

#[derive(Default)]
enum StringSetType {
    #[default]
    None,
    NX,
    XX,
}

pub struct CommandGet {
    key: Vec<u8>,
}

impl CommandGet {
    const ATTR: CmdAttr = CmdAttr::new("GET", 2);

    pub fn parse(req: &RedisRequest) -> Result<CommandGet, RedisError> {
        if !Self::ATTR.check_arity(req.get_args().len() as i32 + 2) {
            return Err(RedisError::InvalidArg);
        }

        Ok(CommandGet {
            key: req.get_key().to_vec(),
        })
    }

    pub async fn execute<E, L, F>(
        &self,
        ctx: Context,
        storage: Storage<E, L, F>,
    ) -> Result<RedisResponse>
    where
        E: Engine,
        L: LockManager,
        F: KvFormat,
    {
        let future = storage.raw_get(ctx, "".to_string(), self.key.clone());

        async move {
            let value = future.await?;
            let mut resp = RedisResponse::new();
            resp.set_type(ContentType::Bulk);

            match value {
                None => resp.set_not_found(true),
                Some(mut val) => {
                    let metadata = MetaData::parse(RedisType::String, &mut val)?;
                    if metadata.expired() {
                        resp.set_not_found(true);
                    } else {
                        resp.set_message(val.to_vec())
                    }
                }
            }
            Ok(resp)
        }
        .await
    }
}

pub struct CommandSet {
    key: Vec<u8>,
    value: Vec<u8>,
    expire: u64,
    get: bool,
    keep_ttl: bool,
    set_flag: StringSetType,
}

impl CommandSet {
    const ATTR: CmdAttr = CmdAttr::new("SET", -3);

    pub fn parse(req: &RedisRequest) -> Result<CommandSet, RedisError> {
        if !Self::ATTR.check_arity(req.get_args().len() as i32 + 2) {
            return Err(RedisError::InvalidArg);
        }

        let mut command = CommandSet {
            key: req.get_key().to_vec(),
            value: req.get_args()[0].to_vec(),
            expire: 0,
            get: false,
            keep_ttl: false,
            set_flag: Default::default(),
        };

        let mut parser = CommandParser::new(req.get_args().iter());
        parser.skip(1);

        let mut ttl_flag = String::new();
        let mut set_flag = String::new();
        while parser.good() {
            if let Ok(Some(v)) = parse_expire_flags(&mut parser, &mut ttl_flag) {
                command.expire = v;
            } else if parser.eat_eq_icase_flag("KEEPTTL", &mut ttl_flag) {
                command.keep_ttl = true;
            } else if parser.eat_eq_icase_flag("NX", &mut set_flag) {
                command.set_flag = StringSetType::NX;
            } else if parser.eat_eq_icase_flag("XX", &mut set_flag) {
                command.set_flag = StringSetType::XX;
            } else if parser.eat_eq_icase("GET") {
                command.get = true;
            } else {
                return Err(RedisError::InvalidArg);
            }
        }

        Ok(command)
    }

    pub async fn execute<E, L, F>(
        &self,
        ctx: Context,
        storage: Storage<E, L, F>,
    ) -> Result<RedisResponse>
    where
        E: Engine,
        L: LockManager,
        F: KvFormat,
    {
        let (cb, future) = paired_future_callback();
        let result = storage.raw_put(
            ctx,
            CF_DEFAULT.to_string(),
            self.key.clone(),
            self.value.clone(),
            0,
            cb,
        );

        let resp = match result {
            Ok(_) => {
                let mut response = RedisResponse::new();
                response.set_type(ContentType::Simple);
                response.set_message("OK".as_bytes().to_vec());
                response
            }
            Err(e) => e.into(),
        };
        Ok(resp)
    }
}
