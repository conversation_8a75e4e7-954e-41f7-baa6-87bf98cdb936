# This file is automatically @generated by Cargo.
# It is not intended for manual editing.
version = 3

[[package]]
name = "addr2line"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e61f2b7f93d2c7d2b08263acaa4a363b3e276806c68af6134c44f523bf1aacd"
dependencies = [
 "gimli",
]

[[package]]
name = "adler"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f26201604c87b1e01bd3d98f8d5d9a8fcbb815e8cedb41ffccbeb4bf593a35fe"

[[package]]
name = "adler32"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d2e7343e7fc9de883d1b0341e0b13970f764c14101234857d2ddafa1cb1cac2"

[[package]]
name = "afl"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59206260f98d163b3ca42fb29fe551dbcda1d43cf70a244066b2a0666a8fb2a9"
dependencies = [
 "cc",
 "clap 2.33.0",
 "rustc_version 0.2.3",
 "xdg",
]

[[package]]
name = "ahash"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43bb833f0bf979d8475d38fbf09ed3b8a55e1885fe93ad3f93239fc6a4f17b98"
dependencies = [
 "getrandom 0.2.3",
 "once_cell",
 "version_check 0.9.4",
]

[[package]]
name = "aho-corasick"
version = "0.7.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e37cfd5e7657ada45f742d6e99ca5788580b5c529dc78faf11ece6dc702656f"
dependencies = [
 "memchr",
]

[[package]]
name = "ansi_term"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee49baf6cb617b853aa8d93bf420db2383fab46d314482ca2803b40d5fde979b"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "anyhow"
version = "1.0.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7825f6833612eb2414095684fcf6c635becf3ce97fe48cf6421321e93bfbd53c"

[[package]]
name = "api_version"
version = "0.1.0"
dependencies = [
 "bitflags",
 "codec",
 "engine_traits",
 "kvproto",
 "match-template",
 "panic_hook",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "txn_types",
]

[[package]]
name = "arbitrary"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16971f2f0ce65c5cf2a1546cc6a0af102ecb11e265ddaa9433fb3e5bfdf676a4"

[[package]]
name = "arc-swap"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dabe5a181f83789739c194cbe5a897dde195078fac08568d09221fd6137a7ba8"

[[package]]
name = "arrayvec"
version = "0.4.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8d73f9beda665eaa98ab9e4f7442bd4e7de6652587de55b2525e52e29c1b0ba"
dependencies = [
 "nodrop",
]

[[package]]
name = "arrow"
version = "13.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c6bee230122beb516ead31935a61f683715f987c6f003eff44ad6986624105a"
dependencies = [
 "bitflags",
 "chrono",
 "csv",
 "flatbuffers",
 "half",
 "hex 0.4.2",
 "indexmap",
 "lazy_static",
 "lexical-core",
 "multiversion",
 "num 0.4.0",
 "rand 0.8.5",
 "regex",
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "async-channel"
version = "1.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2114d64672151c0c5eaa5e131ec84a74f06e1e559830dabba01ca30605d66319"
dependencies = [
 "concurrent-queue",
 "event-listener",
 "futures-core",
]

[[package]]
name = "async-compression"
version = "0.3.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "345fd392ab01f746c717b1357165b76f0b67a60192007b234058c9045fdcf695"
dependencies = [
 "futures-core",
 "futures-io",
 "memchr",
 "pin-project-lite",
 "tokio",
 "zstd",
 "zstd-safe",
]

[[package]]
name = "async-speed-limit"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "481ce9cb6a828f4679495f7376cb6779978d925dd9790b99b48d1bbde6d0f00b"
dependencies = [
 "futures-core",
 "futures-io",
 "futures-timer",
 "pin-project-lite",
]

[[package]]
name = "async-stream"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58982858be7540a465c790b95aaea6710e5139bf8956b1d1344d014fa40100b0"
dependencies = [
 "async-stream-impl 0.2.0",
 "futures-core",
]

[[package]]
name = "async-stream"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dad5c83079eae9969be7fadefe640a1c566901f05ff91ab221de4b6f68d9507e"
dependencies = [
 "async-stream-impl 0.3.3",
 "futures-core",
]

[[package]]
name = "async-stream-impl"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "393356ed99aa7bff0ac486dde592633b83ab02bd254d8c209d5b9f1d0f533480"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "async-stream-impl"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10f203db73a71dfa2fb6dd22763990fa26f3d2625a6da2da900d23b87d26be27"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "async-trait"
version = "0.1.58"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e805d94e6b5001b651426cf4cd446b1ab5f319d27bab5c644f61de0a804360c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "atomic"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3410529e8288c463bedb5930f82833bc0c90e5d2fe639a56582a4d09220b281"
dependencies = [
 "autocfg",
]

[[package]]
name = "atty"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1803c647a3ec87095e7ae7acfca019e98de5ec9a7d01343f611cf3152ed71a90"
dependencies = [
 "libc 0.2.132",
 "winapi 0.3.9",
]

[[package]]
name = "autocfg"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d468802bab17cbc0cc575e9b053f41e72aa36bfa6b7f55e3529ffa43161b97fa"

[[package]]
name = "axum"
version = "0.5.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acee9fd5073ab6b045a275b3e709c163dd36c90685219cb21804a147b58dba43"
dependencies = [
 "async-trait",
 "axum-core",
 "bitflags",
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "hyper",
 "itoa 1.0.1",
 "matchit",
 "memchr",
 "mime",
 "percent-encoding",
 "pin-project-lite",
 "serde",
 "sync_wrapper",
 "tokio",
 "tower",
 "tower-http",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-core"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37e5939e02c56fecd5c017c37df4238c0a839fa76b7f97acdd7efb804fd181cc"
dependencies = [
 "async-trait",
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "mime",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "backtrace"
version = "0.3.61"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7a905d892734eea339e896738c14b9afce22b5318f64b951e70bf3844419b01"
dependencies = [
 "addr2line",
 "cc",
 "cfg-if 1.0.0",
 "libc 0.2.132",
 "miniz_oxide 0.4.4",
 "object",
 "rustc-demangle",
]

[[package]]
name = "backup"
version = "0.0.1"
dependencies = [
 "api_version",
 "async-channel",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crc64fast",
 "encryption",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "external_storage",
 "external_storage_export",
 "file_system",
 "futures 0.3.15",
 "futures-util",
 "grpcio",
 "hex 0.4.2",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "pd_client",
 "prometheus",
 "raft",
 "raftstore",
 "rand 0.8.5",
 "security",
 "serde",
 "slog",
 "slog-global",
 "tempfile",
 "thiserror",
 "tidb_query_common",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "tokio-stream",
 "txn_types",
 "yatp",
]

[[package]]
name = "backup-stream"
version = "0.1.0"
dependencies = [
 "async-compression",
 "async-trait",
 "bytes",
 "chrono",
 "concurrency_manager",
 "crossbeam",
 "crossbeam-channel",
 "dashmap",
 "engine_panic",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "etcd-client",
 "external_storage",
 "external_storage_export",
 "fail",
 "file_system",
 "futures 0.3.15",
 "futures-io",
 "grpcio",
 "hex 0.4.2",
 "indexmap",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "openssl",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raftstore",
 "rand 0.8.5",
 "regex",
 "resolved_ts",
 "security",
 "slog",
 "slog-global",
 "tempdir",
 "tempfile",
 "test_raftstore",
 "test_util",
 "thiserror",
 "tidb_query_datatype",
 "tikv",
 "tikv_alloc",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tonic",
 "txn_types",
 "url",
 "uuid 0.8.2",
 "walkdir",
 "yatp",
]

[[package]]
name = "base64"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "904dfeac50f3cdaba28fc6f57fdcddb75f49ed61346676a78c4ffe55877802fd"

[[package]]
name = "batch-system"
version = "0.1.0"
dependencies = [
 "collections",
 "criterion",
 "crossbeam",
 "derive_more",
 "fail",
 "file_system",
 "lazy_static",
 "online_config",
 "prometheus",
 "serde",
 "slog",
 "slog-global",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "bcc"
version = "0.0.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5dbbe5cc2887bc0bc8506b26dcd4c41d1b54bdf4ff1de8e12d404deee60e4ec"
dependencies = [
 "bcc-sys",
 "bitflags",
 "byteorder",
 "libc 0.2.132",
 "regex",
 "thiserror",
]

[[package]]
name = "bcc-sys"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42d3c07869b846ba3306739375e9ed2f8055a8759fcf7f72ab7bf3bc4df38b9b"

[[package]]
name = "bindgen"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd4865004a46a0aafb2a0a5eb19d3c9fc46ee5f063a6cfc605c69ac9ecf5263d"
dependencies = [
 "bitflags",
 "cexpr 0.4.0",
 "clang-sys",
 "lazy_static",
 "lazycell",
 "peeking_take_while",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash",
 "shlex 0.1.1",
]

[[package]]
name = "bindgen"
version = "0.59.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bd2a9a458e8f4304c52c43ebb0cfbd520289f8379a52e329a38afda99bf8eb8"
dependencies = [
 "bitflags",
 "cexpr 0.6.0",
 "clang-sys",
 "clap 2.33.0",
 "env_logger",
 "lazy_static",
 "lazycell",
 "log",
 "peeking_take_while",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash",
 "shlex 1.1.0",
 "which",
]

[[package]]
name = "bit_field"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dcb6dd1c2376d2e096796e234a70e17e94cc2d5d54ff8ce42b28cef1d0d359a4"

[[package]]
name = "bitfield"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46afbd2983a5d5a7bd740ccb198caf5b82f45c40c09c0eed36052d91cb92e719"

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "block-buffer"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4152116fd6e9dadb291ae18fc1ec3575ed6d84c29642d97890f4b4a3417297e4"
dependencies = [
 "generic-array",
]

[[package]]
name = "boolinator"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfa8873f51c92e232f9bac4065cddef41b714152812bfc5f7672ba16d6ef8cd9"

[[package]]
name = "bstr"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d6c2c5b58ab920a4f5aeaaca34b4488074e8cc7596af94e6f8c6ff247c60245"
dependencies = [
 "lazy_static",
 "memchr",
 "regex-automata",
 "serde",
]

[[package]]
name = "bumpalo"
version = "3.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12ae9db68ad7fac5fe51304d20f016c911539251075a214f8e663babefa35187"

[[package]]
name = "bytemuck"
version = "1.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cdead85bdec19c194affaeeb670c0e41fe23de31459efd1c174d049269cf02cc"

[[package]]
name = "byteorder"
version = "1.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08c48aae112d48ed9f069b33538ea9e3e90aa263cfa3d1c24309612b1f7472de"

[[package]]
name = "bytes"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71b6127be86fdcfddb610f7182ac57211d4b18a3e9c82eb2d17662f2227ad6a"

[[package]]
name = "bzip2-sys"
version = "0.1.11+1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "736a955f3fa7875102d57c82b8cac37ec45224a07fd32d58f9f7a186b6cd4cdc"
dependencies = [
 "cc",
 "libc 0.2.132",
 "pkg-config",
]

[[package]]
name = "c2-chacha"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d64d04786e0f528460fc884753cf8dddcc466be308f6026f8e355c41a0e4101"
dependencies = [
 "lazy_static",
 "ppv-lite86",
]

[[package]]
name = "cache-padded"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "631ae5198c9be5e753e5cc215e1bd73c2b466a3565173db433f52bb9d3e66dba"

[[package]]
name = "callgrind"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7f788eaf239475a3c1e1acf89951255a46c4b9b46cf3e866fc4d0707b4b9e36"
dependencies = [
 "libc 0.2.132",
 "valgrind_request",
]

[[package]]
name = "cargo_metadata"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46e3374c604fb39d1a2f35ed5e4a4e30e60d01fab49446e08f1b3e9a90aef202"
dependencies = [
 "semver 0.9.0",
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "case_macros"
version = "0.1.0"

[[package]]
name = "cast"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "926013f2860c46252efceabb19f4a6b308197505082c609025aa6706c011d427"

[[package]]
name = "causal_ts"
version = "0.0.1"
dependencies = [
 "api_version",
 "async-trait",
 "criterion",
 "engine_rocks",
 "engine_traits",
 "enum_dispatch",
 "error_code",
 "fail",
 "futures 0.3.15",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "parking_lot 0.12.0",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "raft",
 "serde",
 "slog",
 "slog-global",
 "test_pd_client",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "cc"
version = "1.0.73"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fff2a6927b3bb87f9595d67196a70493f627687a71d87a0d692242c33f58c11"
dependencies = [
 "jobserver",
]

[[package]]
name = "cdc"
version = "0.0.1"
dependencies = [
 "api_version",
 "bitflags",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "criterion",
 "crossbeam",
 "engine_rocks",
 "engine_traits",
 "fail",
 "futures 0.3.15",
 "futures-timer",
 "getset",
 "grpcio",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "raftstore",
 "resolved_ts",
 "security",
 "semver 1.0.4",
 "slog",
 "slog-global",
 "tempfile",
 "test_pd_client",
 "test_raftstore",
 "test_util",
 "thiserror",
 "tikv",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "cexpr"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4aedb84272dbe89af497cf81375129abda4fc0a9e7c5d317498c15cc30c0d27"
dependencies = [
 "nom 5.1.0",
]

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom 7.1.0",
]

[[package]]
name = "cfg-if"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4785bdd1c96b2a846b2bd7cc02e86b6b3dbf14e7e53446c4f54c92a361040822"

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "chrono"
version = "0.4.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80094f509cf8b5ae86a4966a39b3ff66cd7e2a3e594accec3743ff3fabeab5b2"
dependencies = [
 "num-integer",
 "num-traits",
 "serde",
 "time",
]

[[package]]
name = "chrono-tz"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0e430fad0384e4defc3dc6b1223d1b886087a8bf9b7080e5ae027f73851ea15"
dependencies = [
 "chrono",
 "parse-zoneinfo",
]

[[package]]
name = "clang-sys"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f54d78e30b388d4815220c8dd03fea5656b6c6d32adb59e89061552a102f8da1"
dependencies = [
 "glob",
 "libc 0.2.132",
 "libloading",
]

[[package]]
name = "clap"
version = "2.33.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5067f5bb2d80ef5d68b4c87db81601f0b75bca627bc2ef76b141d7b846a3c6d9"
dependencies = [
 "ansi_term",
 "atty",
 "bitflags",
 "strsim 0.8.0",
 "textwrap 0.11.0",
 "unicode-width",
 "vec_map",
]

[[package]]
name = "clap"
version = "3.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8c93436c21e4698bacadf42917db28b23017027a4deccb35dbe47a7e7840123"
dependencies = [
 "atty",
 "bitflags",
 "clap_derive",
 "indexmap",
 "lazy_static",
 "os_str_bytes",
 "strsim 0.10.0",
 "termcolor",
 "textwrap 0.15.0",
]

[[package]]
name = "clap_derive"
version = "3.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da95d038ede1a964ce99f49cbe27a7fb538d1da595e4b4f70b8c8f338d17bf16"
dependencies = [
 "heck 0.4.0",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "cloud"
version = "0.0.1"
dependencies = [
 "async-trait",
 "derive_more",
 "error_code",
 "fail",
 "futures-io",
 "kvproto",
 "lazy_static",
 "openssl",
 "prometheus",
 "protobuf",
 "rusoto_core",
 "thiserror",
 "tikv_util",
 "url",
]

[[package]]
name = "cmake"
version = "0.1.48"
source = "git+https://github.com/rust-lang/cmake-rs#00e6b220342a8b0ec4548071928ade38fd5f691b"
dependencies = [
 "cc",
]

[[package]]
name = "codec"
version = "0.0.1"
dependencies = [
 "byteorder",
 "bytes",
 "error_code",
 "libc 0.2.132",
 "panic_hook",
 "protobuf",
 "rand 0.8.5",
 "static_assertions",
 "thiserror",
 "tikv_alloc",
]

[[package]]
name = "collections"
version = "0.1.0"
dependencies = [
 "fxhash",
 "tikv_alloc",
]

[[package]]
name = "concurrency_manager"
version = "0.0.1"
dependencies = [
 "criterion",
 "crossbeam-skiplist",
 "fail",
 "futures 0.3.15",
 "kvproto",
 "parking_lot 0.12.0",
 "rand 0.8.5",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "concurrent-queue"
version = "1.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30ed07550be01594c6026cff2a1d7fe9c8f683caa798e12b68694ac9e88286a3"
dependencies = [
 "cache-padded",
]

[[package]]
name = "coprocessor_plugin_api"
version = "0.1.0"
dependencies = [
 "async-trait",
 "atomic",
 "rustc_version 0.3.3",
]

[[package]]
name = "core-foundation"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a89e2ae426ea83155dccf10c0fa6b1463ef6d5fcb44cee0b224a408fa640a62"
dependencies = [
 "core-foundation-sys",
 "libc 0.2.132",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea221b5284a47e40033bf9b66f35f984ec0ea2931eb03505246cd27a963f981b"

[[package]]
name = "cpu-time"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9e393a7668fe1fad3075085b86c781883000b4ede868f43627b34a87c8b7ded"
dependencies = [
 "libc 0.2.132",
 "winapi 0.3.9",
]

[[package]]
name = "cpuid-bool"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8aebca1129a03dc6dc2b127edd729435bbc4a37e1d5f4d7513165089ceb02634"

[[package]]
name = "crc32fast"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba125de2af0df55319f41944744ad91c71113bf74a4646efff39afe1f6842db1"
dependencies = [
 "cfg-if 0.1.10",
]

[[package]]
name = "crc64fast"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a82510de0a7cadd51dc68ff17da70aea0c80157f902230f9b157cecc2566318"

[[package]]
name = "criterion"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1604dafd25fba2fe2d5895a9da139f8dc9b319a5fe5354ca137cbbce4e178d10"
dependencies = [
 "atty",
 "cast",
 "clap 2.33.0",
 "criterion-plot",
 "csv",
 "itertools",
 "lazy_static",
 "num-traits",
 "oorandom",
 "plotters",
 "rayon",
 "regex",
 "serde",
 "serde_cbor",
 "serde_derive",
 "serde_json",
 "tinytemplate",
 "walkdir",
]

[[package]]
name = "criterion-cpu-time"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63aaaf47e457badbcb376c65a49d0f182c317ebd97dc6d1ced94c8e1d09c0f3a"
dependencies = [
 "criterion",
 "libc 0.2.132",
]

[[package]]
name = "criterion-perf-events"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eba5111e09fabb08bfaedbe28c832876bb38d4f9519f715466332880d80b0eac"
dependencies = [
 "criterion",
 "perfcnt",
]

[[package]]
name = "criterion-plot"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d00996de9f2f7559f7f4dc286073197f83e92256a59ed395f9aac01fe717da57"
dependencies = [
 "cast",
 "itertools",
]

[[package]]
name = "crossbeam"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ae5588f6b3c3cb05239e90bd110f257254aecd01e4635400391aeae07497845"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-channel",
 "crossbeam-deque",
 "crossbeam-epoch 0.9.8",
 "crossbeam-queue",
 "crossbeam-utils 0.8.8",
]

[[package]]
name = "crossbeam-channel"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2dd04ddaf88237dc3b8d8f9a3c1004b506b54b3313403944054d23c0870c521"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.8",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.2"
source = "git+https://github.com/crossbeam-rs/crossbeam?rev=41ed3d948720f26149b2ebeaf58fe8a193134056#41ed3d948720f26149b2ebeaf58fe8a193134056"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-epoch 0.9.10",
 "crossbeam-utils 0.8.11",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.3"
source = "git+https://github.com/tikv/crossbeam.git?branch=tikv-5.0#e0e083d062649484188b7337fe388fd12f2c8d94"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.3",
 "lazy_static",
 "memoffset",
 "scopeguard",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1145cf131a2c6ba0615079ab6a638f7e1973ac9c2634fcbeaaad6114246efe8c"
dependencies = [
 "autocfg",
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.8",
 "lazy_static",
 "memoffset",
 "scopeguard",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.10"
source = "git+https://github.com/crossbeam-rs/crossbeam?rev=41ed3d948720f26149b2ebeaf58fe8a193134056#41ed3d948720f26149b2ebeaf58fe8a193134056"
dependencies = [
 "autocfg",
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.11",
 "memoffset",
 "once_cell",
 "scopeguard",
]

[[package]]
name = "crossbeam-queue"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f25d8400f4a7a5778f0e4e52384a48cbd9b5c495d110786187fc750075277a2"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.8",
]

[[package]]
name = "crossbeam-skiplist"
version = "0.0.0"
source = "git+https://github.com/tikv/crossbeam.git?branch=tikv-5.0#e0e083d062649484188b7337fe388fd12f2c8d94"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-epoch 0.9.3",
 "crossbeam-utils 0.8.3",
 "scopeguard",
]

[[package]]
name = "crossbeam-utils"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3c7c73a2d1e9fc0886a08b93e98eb643461230d5f1925e4036204d5f2e261a8"
dependencies = [
 "autocfg",
 "cfg-if 0.1.10",
 "lazy_static",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.3"
source = "git+https://github.com/tikv/crossbeam.git?branch=tikv-5.0#e0e083d062649484188b7337fe388fd12f2c8d94"
dependencies = [
 "autocfg",
 "cfg-if 1.0.0",
 "lazy_static",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bf124c720b7686e3c2663cf54062ab0f68a88af2fb6a030e87e30bf721fcb38"
dependencies = [
 "cfg-if 1.0.0",
 "lazy_static",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.11"
source = "git+https://github.com/crossbeam-rs/crossbeam?rev=41ed3d948720f26149b2ebeaf58fe8a193134056#41ed3d948720f26149b2ebeaf58fe8a193134056"
dependencies = [
 "cfg-if 1.0.0",
 "once_cell",
]

[[package]]
name = "crypto-mac"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4857fd85a0c34b3c3297875b747c1e02e06b6a0ea32dd892d8192b9ce0813ea6"
dependencies = [
 "generic-array",
 "subtle",
]

[[package]]
name = "csv"
version = "1.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "22813a6dc45b335f9bade10bf7271dc477e81113e89eb251a0bc2a8a81c536e1"
dependencies = [
 "bstr",
 "csv-core",
 "itoa 0.4.4",
 "ryu",
 "serde",
]

[[package]]
name = "csv-core"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b5cadb6b25c77aeff80ba701712494213f4a8418fcda2ee11b6560c3ad0bf4c"
dependencies = [
 "memchr",
]

[[package]]
name = "darling"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fe629a532efad5526454efb0700f86d5ad7ff001acb37e431c8bf017a432a8e"
dependencies = [
 "darling_core",
 "darling_macro",
]

[[package]]
name = "darling_core"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee54512bec54b41cf2337a22ddfadb53c7d4c738494dc2a186d7b037ad683b85"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim 0.9.2",
 "syn 1.0.103",
]

[[package]]
name = "darling_macro"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0cd3e432e52c0810b72898296a69d66b1d78d1517dff6cde7a130557a55a62c1"
dependencies = [
 "darling_core",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "dashmap"
version = "5.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0834a35a3fce649144119e18da2a4d8ed12ef3862f47183fd46f625d072d96c"
dependencies = [
 "cfg-if 1.0.0",
 "num_cpus",
 "parking_lot 0.12.0",
]

[[package]]
name = "debugid"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef552e6f588e446098f6ba40d89ac146c8c7b64aade83c051ee00bb5d2bc18d"
dependencies = [
 "uuid 1.2.1",
]

[[package]]
name = "derivative"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "derive_more"
version = "0.99.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a806e96c59a76a5ba6e18735b6cf833344671e61e7863f2edb5c518ea2cac95c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "digest"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3dd60d1080a57a05ab032377049e0591415d2b31afd7028356dbf3cc6dcb066"
dependencies = [
 "generic-array",
]

[[package]]
name = "dirs-next"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b98cf8ebf19c3d1b223e151f99a4f9f0690dca41414773390fc824184ac833e1"
dependencies = [
 "cfg-if 1.0.0",
 "dirs-sys-next",
]

[[package]]
name = "dirs-sys-next"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"
dependencies = [
 "libc 0.2.132",
 "redox_users",
 "winapi 0.3.9",
]

[[package]]
name = "doc-comment"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "923dea538cea0aa3025e8685b20d6ee21ef99c4f77e954a30febbaac5ec73a97"

[[package]]
name = "either"
version = "1.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e78d4f1cc4ae33bbfc157ed5d5a5ef3bc29227303d595861deb238fcec4e9457"

[[package]]
name = "encoding_rs"
version = "0.8.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a74ea89a0a1b98f6332de42c95baff457ada66d1cb4030f9ff151b2041a1c746"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "encoding_rs"
version = "0.8.29"
source = "git+https://github.com/xiongjiwei/encoding_rs.git?rev=68e0bc5a72a37a78228d80cd98047326559cf43c#68e0bc5a72a37a78228d80cd98047326559cf43c"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "encryption"
version = "0.0.1"
dependencies = [
 "async-trait",
 "byteorder",
 "bytes",
 "crc32fast",
 "crossbeam",
 "derive_more",
 "engine_traits",
 "error_code",
 "fail",
 "file_system",
 "futures 0.3.15",
 "futures-util",
 "hex 0.4.2",
 "kvproto",
 "lazy_static",
 "matches",
 "online_config",
 "openssl",
 "prometheus",
 "protobuf",
 "rand 0.8.5",
 "serde",
 "slog",
 "slog-global",
 "tempfile",
 "test_util",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "toml",
]

[[package]]
name = "encryption_export"
version = "0.0.1"
dependencies = [
 "async-trait",
 "cloud",
 "derive_more",
 "encryption",
 "error_code",
 "file_system",
 "kvproto",
 "openssl",
 "protobuf",
 "rust-ini",
 "slog",
 "slog-global",
 "structopt",
 "tikv_util",
]

[[package]]
name = "engine_panic"
version = "0.0.1"
dependencies = [
 "engine_traits",
 "kvproto",
 "raft",
 "tikv_alloc",
 "tikv_util",
 "tracker",
 "txn_types",
]

[[package]]
name = "engine_rocks"
version = "0.0.1"
dependencies = [
 "api_version",
 "case_macros",
 "collections",
 "derive_more",
 "encryption",
 "engine_traits",
 "fail",
 "file_system",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "num_cpus",
 "online_config",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "rand 0.8.5",
 "regex",
 "rocksdb",
 "serde",
 "slog",
 "slog-global",
 "slog_derive",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
 "time",
 "toml",
 "tracker",
 "txn_types",
]

[[package]]
name = "engine_rocks_helper"
version = "0.1.0"
dependencies = [
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "fail",
 "futures 0.3.15",
 "keys",
 "kvproto",
 "lazy_static",
 "pd_client",
 "prometheus",
 "protobuf",
 "raftstore",
 "slog",
 "slog-global",
 "tempfile",
 "tikv_util",
]

[[package]]
name = "engine_test"
version = "0.0.1"
dependencies = [
 "collections",
 "encryption",
 "engine_panic",
 "engine_rocks",
 "engine_traits",
 "file_system",
 "raft_log_engine",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "engine_tirocks"
version = "0.1.0"
dependencies = [
 "api_version",
 "codec",
 "collections",
 "derive_more",
 "engine_traits",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "prometheus",
 "prometheus-static-metric",
 "rand 0.8.5",
 "slog",
 "slog-global",
 "slog_derive",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
 "tirocks",
 "tracker",
 "txn_types",
]

[[package]]
name = "engine_traits"
version = "0.0.1"
dependencies = [
 "case_macros",
 "error_code",
 "fail",
 "file_system",
 "kvproto",
 "log_wrappers",
 "protobuf",
 "raft",
 "serde",
 "slog",
 "slog-global",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "toml",
 "tracker",
 "txn_types",
]

[[package]]
name = "engine_traits_tests"
version = "0.0.1"
dependencies = [
 "engine_test",
 "engine_traits",
 "panic_hook",
 "tempfile",
 "tikv_alloc",
]

[[package]]
name = "enum_dispatch"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eb359f1476bf611266ac1f5355bc14aeca37b299d0ebccc038ee7058891c9cb"
dependencies = [
 "once_cell",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "env_logger"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b2cf0344971ee6c64c31be0d530793fba457d322dfec2810c453d0ef228f9c3"
dependencies = [
 "atty",
 "humantime",
 "log",
 "regex",
 "termcolor",
]

[[package]]
name = "error-chain"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ab49e9dcb602294bc42f9a7dfc9bc6e936fca4418ea300dbfb84fe16de0b7d9"
dependencies = [
 "backtrace",
 "version_check 0.1.5",
]

[[package]]
name = "error_code"
version = "0.0.1"
dependencies = [
 "grpcio",
 "kvproto",
 "lazy_static",
 "raft",
 "tikv_alloc",
]

[[package]]
name = "etcd-client"
version = "0.10.2"
source = "git+https://github.com/pingcap/etcd-client?rev=14a6f8731f1890d5fd2f6e16a9f0d0a306b0599e#14a6f8731f1890d5fd2f6e16a9f0d0a306b0599e"
dependencies = [
 "http",
 "hyper",
 "hyper-openssl",
 "openssl",
 "prost",
 "tokio",
 "tokio-stream",
 "tonic",
 "tonic-build",
 "tower",
 "tower-service",
 "visible",
]

[[package]]
name = "event-listener"
version = "2.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7531096570974c3a9dcf9e4b8e1cede1ec26cf5046219fb3b9d897503b9be59"

[[package]]
name = "example_coprocessor_plugin"
version = "0.1.0"
dependencies = [
 "coprocessor_plugin_api",
]

[[package]]
name = "external_storage"
version = "0.0.1"
dependencies = [
 "async-compression",
 "async-trait",
 "bytes",
 "encryption",
 "engine_traits",
 "fail",
 "ffi-support",
 "file_system",
 "futures 0.3.15",
 "futures-executor",
 "futures-io",
 "futures-util",
 "grpcio",
 "kvproto",
 "lazy_static",
 "libloading",
 "matches",
 "openssl",
 "prometheus",
 "protobuf",
 "rand 0.8.5",
 "rusoto_core",
 "rust-ini",
 "slog",
 "slog-global",
 "structopt",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "tokio-util",
 "url",
]

[[package]]
name = "external_storage_export"
version = "0.0.1"
dependencies = [
 "async-compression",
 "async-trait",
 "cloud",
 "encryption",
 "engine_traits",
 "external_storage",
 "ffi-support",
 "file_system",
 "futures 0.3.15",
 "futures-executor",
 "futures-io",
 "futures-util",
 "grpcio",
 "kvproto",
 "lazy_static",
 "libc 0.2.132",
 "libloading",
 "matches",
 "nix 0.24.1",
 "once_cell",
 "protobuf",
 "rust-ini",
 "signal-hook",
 "slog",
 "slog-global",
 "slog-term",
 "structopt",
 "tempfile",
 "tikv_util",
 "tokio",
 "tokio-util",
 "url",
]

[[package]]
name = "fail"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec3245a0ca564e7f3c797d20d833a6870f57a728ac967d5225b3ffdef4465011"
dependencies = [
 "lazy_static",
 "log",
 "rand 0.8.5",
]

[[package]]
name = "farmhash"
version = "1.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f35ce9c8fb9891c75ceadbc330752951a4e369b50af10775955aeb9af3eee34b"

[[package]]
name = "ffi-support"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f85d4d1be103c0b2d86968f0b0690dc09ac0ba205b90adb0389b552869e5000e"
dependencies = [
 "lazy_static",
 "log",
]

[[package]]
name = "file_system"
version = "0.1.0"
dependencies = [
 "bcc",
 "collections",
 "crc32fast",
 "crossbeam-utils 0.8.8",
 "fs2",
 "lazy_static",
 "libc 0.2.132",
 "maligned",
 "online_config",
 "openssl",
 "parking_lot 0.12.0",
 "prometheus",
 "prometheus-static-metric",
 "rand 0.8.5",
 "serde",
 "slog",
 "slog-global",
 "strum 0.20.0",
 "tempfile",
 "thread_local",
 "tikv_alloc",
 "tikv_util",
 "tokio",
]

[[package]]
name = "filedescriptor"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed3d8a5e20435ff00469e51a0d82049bae66504b5c429920dadf9bb54d47b3f"
dependencies = [
 "libc 0.2.132",
 "thiserror",
 "winapi 0.3.9",
]

[[package]]
name = "filetime"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d34cfa13a63ae058bfa601fe9e313bbdb3746427c1459185464ce0fcf62e1e8"
dependencies = [
 "cfg-if 1.0.0",
 "libc 0.2.132",
 "redox_syscall 0.2.11",
 "winapi 0.3.9",
]

[[package]]
name = "findshlibs"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d691fdb3f817632d259d09220d4cf0991dbb2c9e59e044a02a59194bf6e14484"
dependencies = [
 "cc",
 "lazy_static",
 "libc 0.2.132",
 "winapi 0.3.9",
]

[[package]]
name = "fixedbitset"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce7134b9999ecaf8bcd65542e436736ef32ddca1b3e06094cb6ec5755203b80"

[[package]]
name = "flatbuffers"
version = "2.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b428b715fdbdd1c364b84573b5fdc0f84f8e423661b9f398735278bc7f2b6a"
dependencies = [
 "bitflags",
 "smallvec",
 "thiserror",
]

[[package]]
name = "flate2"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2adaffba6388640136149e18ed080b77a78611c1e1d6de75aedcdf78df5d4682"
dependencies = [
 "crc32fast",
 "libc 0.2.132",
 "libz-sys",
 "miniz_oxide 0.3.7",
]

[[package]]
name = "fnv"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fad85553e09a6f881f739c29f0b00b0f01357c743266d478b68951ce23285f3"

[[package]]
name = "foreign-types"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6f339eb8adc052cd2ca78910fda869aefa38d22d5cb648e6485e4d3fc06f3b1"
dependencies = [
 "foreign-types-shared",
]

[[package]]
name = "foreign-types-shared"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0228411908ca8685dba7fc2cdd70ec9990a6e753e89b6ac91a84c40fbaf4b"

[[package]]
name = "form_urlencoded"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5fc25a87fa4fd2094bffb06925852034d90a17f0d1e05197d4956d3555752191"
dependencies = [
 "matches",
 "percent-encoding",
]

[[package]]
name = "fs2"
version = "0.4.3"
source = "git+https://github.com/tabokie/fs2-rs?branch=tikv#cd503764a19a99d74c1ab424dd13d6bcd093fcae"
dependencies = [
 "libc 0.2.132",
 "winapi 0.3.9",
]

[[package]]
name = "fs_extra"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f2a4a2034423744d2cc7ca2068453168dcdb82c438419e639a26bd87839c674"

[[package]]
name = "fsevent"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ab7d1bd1bd33cc98b0889831b72da23c0aa4df9cec7e0702f46ecea04b35db6"
dependencies = [
 "bitflags",
 "fsevent-sys",
]

[[package]]
name = "fsevent-sys"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f41b048a94555da0f42f1d632e2e19510084fb8e303b0daa2816e733fb3644a0"
dependencies = [
 "libc 0.2.132",
]

[[package]]
name = "fuchsia-cprng"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a06f77d526c1a601b7c4cdd98f54b5eaabffc14d5f2f0296febdc7f357c6d3ba"

[[package]]
name = "fuchsia-zircon"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e9763c69ebaae630ba35f74888db465e49e259ba1bc0eda7d06f4a067615d82"
dependencies = [
 "bitflags",
 "fuchsia-zircon-sys",
]

[[package]]
name = "fuchsia-zircon-sys"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3dcaa9ae7725d12cdb85b3ad99a434db70b468c09ded17e012d86b5c1010f7a7"

[[package]]
name = "futures"
version = "0.1.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a471a38ef8ed83cd6e40aa59c1ffe17db6855c18e3604d9c4ed8c08ebc28678"

[[package]]
name = "futures"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e7e43a803dae2fa37c1f6a8fe121e1f7bf9548b4dfc0522a42f34145dadfc27"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-channel"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e682a68b29a882df0545c143dc3646daefe80ba479bcdede94d5a703de2871e2"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0402f765d8a89a26043b889b26ce3c4679d268fa6bb22cd7c6aad98340e179d1"

[[package]]
name = "futures-executor"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "badaa6a909fac9e7236d0620a2f57f7664640c56575b71a7552fbd68deafab79"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
 "num_cpus",
]

[[package]]
name = "futures-io"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acc499defb3b348f8d8f3f66415835a9131856ff7714bf10dadfc4ec4bdb29a1"

[[package]]
name = "futures-macro"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4c40298486cdf52cc00cd6d6987892ba502c7656a16a4192a9992b1ccedd121"
dependencies = [
 "autocfg",
 "proc-macro-hack",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "futures-sink"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a57bead0ceff0d6dde8f465ecd96c9338121bb7717d3e7b108059531870c4282"

[[package]]
name = "futures-task"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a16bef9fc1a4dddb5bee51c989e3fbba26569cbb0e31f5b303c184e3dd33dae"

[[package]]
name = "futures-timer"
version = "3.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e64b03909df88034c26dc1547e8970b91f98bdb65165d6a4e9110d94263dbb2c"

[[package]]
name = "futures-util"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "feb5c238d27e2bf94ffdfd27b2c29e3df4a68c4193bb6427384259e2bf191967"
dependencies = [
 "autocfg",
 "futures 0.1.31",
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "proc-macro-hack",
 "proc-macro-nested",
 "slab",
]

[[package]]
name = "fuzz"
version = "0.0.1"
dependencies = [
 "anyhow",
 "cargo_metadata",
 "lazy_static",
 "regex",
 "structopt",
]

[[package]]
name = "fuzz-targets"
version = "0.0.1"
dependencies = [
 "anyhow",
 "byteorder",
 "tidb_query_datatype",
 "tikv_util",
]

[[package]]
name = "fuzzer-afl"
version = "0.0.1"
dependencies = [
 "afl",
 "fuzz-targets",
]

[[package]]
name = "fuzzer-honggfuzz"
version = "0.0.1"
dependencies = [
 "fuzz-targets",
 "honggfuzz",
]

[[package]]
name = "fuzzer-libfuzzer"
version = "0.0.1"
dependencies = [
 "fuzz-targets",
 "libfuzzer-sys",
]

[[package]]
name = "fxhash"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c31b6d751ae2c7f11320402d34e41349dd1016f8d5d45e48c4312bc8625af50c"
dependencies = [
 "byteorder",
]

[[package]]
name = "gag"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a713bee13966e9fbffdf7193af71d54a6b35a0bb34997cd6c9519ebeb5005972"
dependencies = [
 "filedescriptor",
 "tempfile",
]

[[package]]
name = "generic-array"
version = "0.14.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "501466ecc8a30d1d3b7fc9229b122b2ce8ed6e9d9223f1138d4babb253e51817"
dependencies = [
 "typenum",
 "version_check 0.9.4",
]

[[package]]
name = "getrandom"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "473a1265acc8ff1e808cd0a1af8cee3c2ee5200916058a2ca113c29f2d903571"
dependencies = [
 "cfg-if 0.1.10",
 "libc 0.2.132",
 "wasi 0.7.0",
]

[[package]]
name = "getrandom"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcd999463524c52659517fe2cea98493cfe485d10565e7b0fb07dbba7ad2753"
dependencies = [
 "cfg-if 1.0.0",
 "libc 0.2.132",
 "wasi 0.10.2+wasi-snapshot-preview1",
]

[[package]]
name = "getset"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24b328c01a4d71d2d8173daa93562a73ab0fe85616876f02500f53d82948c504"
dependencies = [
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "gimli"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0a01e0497841a3b2db4f8afa483cce65f7e96a3498bd6c541734792aeac8fe7"

[[package]]
name = "glob"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b919933a397b79c37e33b77bb2aa3dc8eb6e165ad809e58ff75bc7db2e34574"

[[package]]
name = "gperftools"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20a3fc5818b1223ec628fc6998c8900486208b577f78c07500d4b52f983ebc9d"
dependencies = [
 "error-chain",
 "lazy_static",
 "pkg-config",
]

[[package]]
name = "grpcio"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f2506de56197d01821c2d1d21082d2dcfd6c82d7a1d6e04d33f37aab6130632"
dependencies = [
 "futures-executor",
 "futures-util",
 "grpcio-sys",
 "libc 0.2.132",
 "log",
 "parking_lot 0.11.1",
 "protobuf",
]

[[package]]
name = "grpcio-compiler"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed97a17310fd00ff4109357584a00244e2a785d05b7ee0ef4d1e8fb1d84266df"
dependencies = [
 "protobuf",
]

[[package]]
name = "grpcio-health"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a37eae605cd21f144b7c7fd0e64e57af9f73d132756fef5b706db110c3ec7ea0"
dependencies = [
 "futures-executor",
 "futures-util",
 "grpcio",
 "log",
 "protobuf",
]

[[package]]
name = "grpcio-sys"
version = "0.10.3+1.44.0-patched"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f23adc509a3c4dea990e0ab8d2add4a65389ee69c288b7851d75dd1df7a6d6c6"
dependencies = [
 "bindgen 0.59.2",
 "cc",
 "cmake",
 "libc 0.2.132",
 "libz-sys",
 "openssl-sys",
 "pkg-config",
 "walkdir",
]

[[package]]
name = "h2"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f9f29bc9dda355256b2916cf526ab02ce0aeaaaf2bad60d65ef3f12f11dd0f4"
dependencies = [
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "futures-util",
 "http",
 "indexmap",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "half"
version = "1.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eabb4a44450da02c90444cf74558da904edde8fb4e9035a9a6a4e15445af0bd7"

[[package]]
name = "hashbrown"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d7afe4a420e3fe79967a00898cc1f4db7c8a49a9333a29f8a4bd76a253d5cd04"

[[package]]
name = "hashbrown"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c21d40587b92fa6a6c6e3c1bdbf87d75511db5672f9c93175574b3a00df1758"
dependencies = [
 "ahash",
]

[[package]]
name = "heck"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20564e78d53d2bb135c343b3f47714a56af2061f1c928fdb541dc7b9fdd94205"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "heck"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2540771e65fc8cb83cd6e8a237f70c319bd5c29f78ed1084ba5d50eeac86f7f9"

[[package]]
name = "hermit-abi"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "307c3c9f937f38e3534b1d6447ecf090cafcc9744e4a6360e8b037b2cf5af120"
dependencies = [
 "libc 0.2.132",
]

[[package]]
name = "hex"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "805026a5d0141ffc30abb3be3173848ad46a1b1664fe632428479619a3644d77"

[[package]]
name = "hex"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "644f9158b2f133fd50f5fb3242878846d9eb792e445c893805ff0e3824006e35"

[[package]]
name = "hmac"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1441c6b1e930e2817404b5046f1f989899143a12bf92de603b69f4e0aee1e15"
dependencies = [
 "crypto-mac",
 "digest",
]

[[package]]
name = "honggfuzz"
version = "0.5.47"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3de2c3273ef7735df1c5a72128ca85b1d20105b9aac643cdfd7a6e581311150"
dependencies = [
 "arbitrary",
 "lazy_static",
 "memmap",
]

[[package]]
name = "http"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75f43d41e26995c17e71ee126451dd3941010b0514a81a9d11f3b341debc2399"
dependencies = [
 "bytes",
 "fnv",
 "itoa 1.0.1",
]

[[package]]
name = "http-body"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d5f38f16d184e36f2408a55281cd658ecbd3ca05cce6d6510a176eca393e26d1"
dependencies = [
 "bytes",
 "http",
 "pin-project-lite",
]

[[package]]
name = "http-range-header"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bfe8eed0a9285ef776bb792479ea3834e8b94e13d615c2f66d03dd50a435a29"

[[package]]
name = "httparse"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d897f394bad6a705d5f4104762e116a75639e470d80901eed05a860a95cb1904"

[[package]]
name = "httpdate"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05842d0d43232b23ccb7060ecb0f0626922c21f30012e97b767b30afd4a5d4b9"

[[package]]
name = "humantime"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a3a5bfb195931eeb336b2a7b4d761daec841b97f947d34394601737a7bba5e4"

[[package]]
name = "hyper"
version = "0.14.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "034711faac9d2166cb1baf1a2fb0b60b1f277f8492fd72176c17f3515e1abd3c"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "httparse",
 "httpdate",
 "itoa 1.0.1",
 "pin-project-lite",
 "socket2",
 "tokio",
 "tower-service",
 "tracing",
 "want",
]

[[package]]
name = "hyper-openssl"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9d52322a69f0a93f177d76ca82073fcec8d5b4eb6e28525d5b3142fa718195c"
dependencies = [
 "http",
 "hyper",
 "linked_hash_set",
 "once_cell",
 "openssl",
 "openssl-sys",
 "parking_lot 0.11.1",
 "tokio",
 "tokio-openssl",
 "tower-layer",
]

[[package]]
name = "hyper-timeout"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbb958482e8c7be4bc3cf272a766a2b0bf1a6755e7a6ae777f017a31d11b13b1"
dependencies = [
 "hyper",
 "pin-project-lite",
 "tokio",
 "tokio-io-timeout",
]

[[package]]
name = "hyper-tls"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6183ddfa99b85da61a140bea0efc93fdf56ceaa041b37d553518030827f9905"
dependencies = [
 "bytes",
 "hyper",
 "native-tls",
 "tokio",
 "tokio-native-tls",
]

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02e2673c30ee86b5b96a9cb52ad15718aa1f966f5ab9ad54a8b95d5ca33120a9"
dependencies = [
 "matches",
 "unicode-bidi",
 "unicode-normalization",
]

[[package]]
name = "if_chain"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb56e1aa765b4b4f3aadfab769793b7087bb03a4ea4920644a6d238e2df5b9ed"

[[package]]
name = "indexmap"
version = "1.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "824845a0bf897a9042383849b02c1bc219c2383772efcd5c6f9766fa4b81aef3"
dependencies = [
 "autocfg",
 "hashbrown 0.9.1",
]

[[package]]
name = "inferno"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16d4bde3a7105e59c66a4104cfe9606453af1c7a0eac78cb7d5bc263eb762a70"
dependencies = [
 "ahash",
 "atty",
 "indexmap",
 "itoa 1.0.1",
 "lazy_static",
 "log",
 "num-format",
 "quick-xml",
 "rgb",
 "str_stack",
]

[[package]]
name = "inotify"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4816c66d2c8ae673df83366c18341538f234a26d65a9ecea5c348b453ac1d02f"
dependencies = [
 "bitflags",
 "inotify-sys",
 "libc 0.2.132",
]

[[package]]
name = "inotify-sys"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e05c02b5e89bff3b946cedeca278abc628fe811e604f027c45a8aa3cf793d0eb"
dependencies = [
 "libc 0.2.132",
]

[[package]]
name = "instant"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a5bbe824c507c5da5956355e86a746d82e0e1464f65d862cc5e71da70e94b2c"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "into_other"
version = "0.0.1"
dependencies = [
 "engine_traits",
 "kvproto",
 "raft",
]

[[package]]
name = "iovec"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2b3ea6ff95e175473f8ffe6a7eb7c00d054240321b84c57051175fe3c1e075e"
dependencies = [
 "libc 0.2.132",
]

[[package]]
name = "ipnet"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47be2f14c678be2fdcab04ab1171db51b2762ce6f0a8ee87c8dd4a04ed216135"

[[package]]
name = "ipnetwork"
version = "0.15.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a69dd5e3613374e74da81c251750153abe3bd0ad17641ea63d43d1e21d0dbd4d"
dependencies = [
 "serde",
]

[[package]]
name = "itertools"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37d572918e350e82412fe766d24b15e6682fb2ed2bbe018280caa810397cb319"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "501266b7edd0174f8530248f87f99c88fbe60ca4ef3dd486835b8d8d53136f7f"

[[package]]
name = "itoa"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1aab8fc367588b89dcee83ab0fd66b72b50b72fa1904d7095045ace2b0c81c35"

[[package]]
name = "jobserver"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2b1d42ef453b30b7387e113da1c83ab1605d90c5b4e0eb8e96d016ed3b8c160"
dependencies = [
 "getrandom 0.1.12",
 "libc 0.2.132",
 "log",
]

[[package]]
name = "js-sys"
version = "0.3.56"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a38fc24e30fd564ce974c02bf1d337caddff65be6cc4735a1f7eab22a7440f04"
dependencies = [
 "wasm-bindgen",
]

[[package]]
name = "kernel32-sys"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7507624b29483431c0ba2d82aece8ca6cdba9382bff4ddd0f7490560c056098d"
dependencies = [
 "winapi 0.2.8",
 "winapi-build",
]

[[package]]
name = "keyed_priority_queue"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d63b6407b66fc81fc539dccf3ddecb669f393c5101b6a2be3976c95099a06e8"
dependencies = [
 "indexmap",
]

[[package]]
name = "keys"
version = "0.1.0"
dependencies = [
 "byteorder",
 "kvproto",
 "log_wrappers",
 "panic_hook",
 "thiserror",
 "tikv_alloc",
]

[[package]]
name = "kvproto"
version = "0.0.2"
dependencies = [
 "futures 0.3.15",
 "grpcio",
 "protobuf",
 "protobuf-build",
 "raft-proto",
]

[[package]]
name = "lazy_static"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2abad23fbc42b3700f2f279844dc832adb2b2eb069b2df918f455c4e18cc646"

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "lexical-core"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92912c4af2e7d9075be3e5e3122c4d7263855fa6cce34fbece4dd08e5884624d"
dependencies = [
 "lexical-parse-float",
 "lexical-parse-integer",
 "lexical-util",
 "lexical-write-float",
 "lexical-write-integer",
]

[[package]]
name = "lexical-parse-float"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f518eed87c3be6debe6d26b855c97358d8a11bf05acec137e5f53080f5ad2dd8"
dependencies = [
 "lexical-parse-integer",
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "lexical-parse-integer"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afc852ec67c6538bbb2b9911116a385b24510e879a69ab516e6a151b15a79168"
dependencies = [
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "lexical-util"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c72a9d52c5c4e62fa2cdc2cb6c694a39ae1382d9c2a17a466f18e272a0930eb1"
dependencies = [
 "static_assertions",
]

[[package]]
name = "lexical-write-float"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a89ec1d062e481210c309b672f73a0567b7855f21e7d2fae636df44d12e97f9"
dependencies = [
 "lexical-util",
 "lexical-write-integer",
 "static_assertions",
]

[[package]]
name = "lexical-write-integer"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "094060bd2a7c2ff3a16d5304a6ae82727cb3cc9d1c70f813cc73f744c319337e"
dependencies = [
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "libc"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e32a70cf75e5846d53a673923498228bbec6a8624708a9ea5645f075d6276122"

[[package]]
name = "libc"
version = "0.2.132"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8371e4e5341c3a96db127eb2465ac681ced4c433e01dd0e938adbef26ba93ba5"

[[package]]
name = "libfuzzer-sys"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb789afcc589a08928d1e466087445ab740a0f70a2ee23d9349a0e3723d65e1b"
dependencies = [
 "arbitrary",
 "cc",
]

[[package]]
name = "libloading"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f84d96438c15fcd6c3f244c8fce01d1e2b9c6b5623e9c711dc9286d8fc92d6a"
dependencies = [
 "cfg-if 1.0.0",
 "winapi 0.3.9",
]

[[package]]
name = "libmimalloc-sys"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2396cf99d2f58611cd69f0efeee4af3d2e2c7b61bed433515029163aa567e65c"
dependencies = [
 "cc",
]

[[package]]
name = "librocksdb_sys"
version = "0.1.0"
source = "git+https://github.com/tikv/rust-rocksdb.git#bd07e9e598db63574cf06edaeea3c4687eadff59"
dependencies = [
 "bindgen 0.57.0",
 "bzip2-sys",
 "cc",
 "cmake",
 "libc 0.2.132",
 "libtitan_sys",
 "libz-sys",
 "lz4-sys",
 "openssl-sys",
 "snappy-sys",
 "tikv-jemalloc-sys",
 "zstd-sys",
]

[[package]]
name = "libtitan_sys"
version = "0.0.1"
source = "git+https://github.com/tikv/rust-rocksdb.git#bd07e9e598db63574cf06edaeea3c4687eadff59"
dependencies = [
 "bzip2-sys",
 "cc",
 "cmake",
 "libc 0.2.132",
 "libz-sys",
 "lz4-sys",
 "snappy-sys",
 "zstd-sys",
]

[[package]]
name = "libz-sys"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de5435b8549c16d423ed0c03dbaafe57cf6c3344744f1242520d59c9d8ecec66"
dependencies = [
 "cc",
 "libc 0.2.132",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "linked-hash-map"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fb9b38af92608140b86b693604b9ffcc5824240a484d1ecd4795bacb2fe88f3"

[[package]]
name = "linked_hash_set"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47186c6da4d81ca383c7c47c1bfc80f4b95f4720514d860a5407aaf4233f9588"
dependencies = [
 "linked-hash-map",
]

[[package]]
name = "lock_api"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88943dd7ef4a2e5a4bfa2753aaab3013e34ce2533d1996fb18ef591e315e2b3b"
dependencies = [
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51b9bbe6c47d51fc3e1a9b945965946b4c44142ab8792c50835a980d362c2710"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "log_wrappers"
version = "0.0.1"
dependencies = [
 "hex 0.4.2",
 "protobuf",
 "slog",
 "slog-term",
 "tikv_alloc",
]

[[package]]
name = "lz4-sys"
version = "1.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dca79aa95d8b3226213ad454d328369853be3a1382d89532a854f4d69640acae"
dependencies = [
 "cc",
 "libc 0.2.132",
]

[[package]]
name = "maligned"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e88c3cbe8288f77f293e48a28b3232e3defd203a6d839fa7f68ea4329e83464"

[[package]]
name = "match-template"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c334ac67725febd94c067736ac46ef1c7cacf1c743ca14b9f917c2df2c20acd8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "matches"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ffc5c5338469d4d3ea17d269fa8ea3512ad247247c30bd2df69e68309ed0a08"

[[package]]
name = "matchit"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73cbba799671b762df5a175adf59ce145165747bb891505c43d09aefbbf38beb"

[[package]]
name = "md-5"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5a279bb9607f9f53c22d496eade00d138d1bdcccd07d74650387cf94942a15"
dependencies = [
 "block-buffer",
 "digest",
 "opaque-debug",
]

[[package]]
name = "memchr"
version = "2.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "308cc39be01b73d0d18f82a0e7b2a3df85245f84af96fdddc5d202d27e47b86a"
dependencies = [
 "libc 0.2.132",
]

[[package]]
name = "memmap"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6585fd95e7bb50d6cc31e20d4cf9afb4e2ba16c5846fc76793f11218da9c475b"
dependencies = [
 "libc 0.2.132",
 "winapi 0.3.9",
]

[[package]]
name = "memmap2"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "057a3db23999c867821a7a59feb06a578fcb03685e983dff90daf9e7d24ac08f"
dependencies = [
 "libc 0.2.132",
]

[[package]]
name = "memoffset"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59accc507f1338036a0477ef61afdae33cde60840f4dfe481319ce3ad116ddf9"
dependencies = [
 "autocfg",
]

[[package]]
name = "memory_trace_macros"
version = "0.1.0"
dependencies = [
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "mimalloc"
version = "0.1.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e7c6b11afd1e5e689ac96b6d18b1fc763398fe3d7eed99e8773426bc2033dfb"
dependencies = [
 "libmimalloc-sys",
]

[[package]]
name = "mime"
version = "0.3.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a60c7ce501c71e03a9c9c0d35b861413ae925bd979cc7a4e30d060069aaac8d"

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "791daaae1ed6889560f8c4359194f56648355540573244a5448a83ba1ecc7435"
dependencies = [
 "adler32",
]

[[package]]
name = "miniz_oxide"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a92518e98c078586bc6c934028adcca4c92a53d6a958196de835170a01d84e4b"
dependencies = [
 "adler",
 "autocfg",
]

[[package]]
name = "mio"
version = "0.6.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4afd66f5b91bf2a3bc13fad0e21caedac168ca4c707504e75585648ae80e4cc4"
dependencies = [
 "cfg-if 0.1.10",
 "fuchsia-zircon",
 "fuchsia-zircon-sys",
 "iovec",
 "kernel32-sys",
 "libc 0.2.132",
 "log",
 "miow",
 "net2",
 "slab",
 "winapi 0.2.8",
]

[[package]]
name = "mio"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d732bc30207a6423068df043e3d02e0735b155ad7ce1a6f76fe2baa5b158de"
dependencies = [
 "libc 0.2.132",
 "log",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.42.0",
]

[[package]]
name = "mio-extras"
version = "2.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52403fe290012ce777c4626790c8951324a2b9e3316b3143779c72b029742f19"
dependencies = [
 "lazycell",
 "log",
 "mio 0.6.23",
 "slab",
]

[[package]]
name = "miow"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebd808424166322d4a38da87083bfddd3ac4c131334ed55856112eb06d46944d"
dependencies = [
 "kernel32-sys",
 "net2",
 "winapi 0.2.8",
 "ws2_32-sys",
]

[[package]]
name = "mmap"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bc85448a6006dd2ba26a385a564a8a0f1f2c7e78c70f1a70b2e0f4af286b823"
dependencies = [
 "libc 0.1.12",
 "tempdir",
]

[[package]]
name = "mnt"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1587ebb20a5b04738f16cffa7e2526f1b8496b84f92920facd518362ff1559eb"
dependencies = [
 "libc 0.2.132",
]

[[package]]
name = "more-asserts"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0debeb9fcf88823ea64d64e4a815ab1643f33127d995978e099942ce38f25238"

[[package]]
name = "multimap"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97fbd5d00e0e37bfb10f433af8f5aaf631e739368dc9fc28286ca81ca4948dc"
dependencies = [
 "serde",
]

[[package]]
name = "multiversion"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "025c962a3dd3cc5e0e520aa9c612201d127dcdf28616974961a649dca64f5373"
dependencies = [
 "multiversion-macros",
]

[[package]]
name = "multiversion-macros"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8a3e2bde382ebf960c1f3e79689fa5941625fe9bf694a1cb64af3e85faff3af"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "murmur3"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ead5388e485d38e622630c6b05afd3761a6701ff15c55b279ea5b31dcb62cff"

[[package]]
name = "native-tls"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8d96b2e1c8da3957d58100b09f102c6d9cfdfced01b7ec5a8974044bb09dbd4"
dependencies = [
 "lazy_static",
 "libc 0.2.132",
 "log",
 "openssl",
 "openssl-probe",
 "openssl-sys",
 "schannel",
 "security-framework",
 "security-framework-sys",
 "tempfile",
]

[[package]]
name = "net2"
version = "0.2.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "391630d12b68002ae1e25e8f974306474966550ad82dac6886fb8910c19568ae"
dependencies = [
 "cfg-if 0.1.10",
 "libc 0.2.132",
 "winapi 0.3.9",
]

[[package]]
name = "nix"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f17df307904acd05aa8e32e97bb20f2a0df1728bbc2d771ae8f9a90463441e9"
dependencies = [
 "bitflags",
 "cfg-if 1.0.0",
 "libc 0.2.132",
 "memoffset",
]

[[package]]
name = "nix"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e322c04a9e3440c327fca7b6c8a63e6890a32fa2ad689db972425f07e0d22abb"
dependencies = [
 "autocfg",
 "bitflags",
 "cfg-if 1.0.0",
 "libc 0.2.132",
 "memoffset",
 "pin-utils",
]

[[package]]
name = "nodrop"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f9667ddcc6cc8a43afc9b7917599d7216aa09c463919ea32c59ed6cac8bc945"

[[package]]
name = "nom"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf51a729ecf40266a2368ad335a5fdde43471f545a967109cd62146ecf8b66ff"

[[package]]
name = "nom"
version = "4.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ad2a91a8e869eeb30b9cb3119ae87773a8f4ae617f41b1eb9c154b2905f7bd6"
dependencies = [
 "memchr",
 "version_check 0.1.5",
]

[[package]]
name = "nom"
version = "5.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c433f4d505fe6ce7ff78523d2fa13a0b9f2690e181fc26168bcbe5ccc5d14e07"
dependencies = [
 "memchr",
 "version_check 0.1.5",
]

[[package]]
name = "nom"
version = "7.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b1d11e1ef389c76fe5b81bcaf2ea32cf88b62bc494e19f493d0b30e7a930109"
dependencies = [
 "memchr",
 "minimal-lexical",
 "version_check 0.9.4",
]

[[package]]
name = "notify"
version = "4.0.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae03c8c853dba7bfd23e571ff0cff7bc9dceb40a4cd684cd1681824183f45257"
dependencies = [
 "bitflags",
 "filetime",
 "fsevent",
 "fsevent-sys",
 "inotify",
 "libc 0.2.132",
 "mio 0.6.23",
 "mio-extras",
 "walkdir",
 "winapi 0.3.9",
]

[[package]]
name = "ntapi"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f26e041cd983acbc087e30fcba770380cfa352d0e392e175b2344ebaf7ea0602"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "num"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab3e176191bc4faad357e3122c4747aa098ac880e88b168f106386128736cf4a"
dependencies = [
 "num-complex 0.3.0",
 "num-integer",
 "num-iter",
 "num-rational 0.3.0",
 "num-traits",
]

[[package]]
name = "num"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43db66d1170d347f9a065114077f7dccb00c1b9478c89384490a3425279a4606"
dependencies = [
 "num-bigint",
 "num-complex 0.4.1",
 "num-integer",
 "num-iter",
 "num-rational 0.4.0",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f93ab6289c7b344a8a9f60f88d80aa20032336fe78da341afc91c8a2341fc75f"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-complex"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b05ad05bd8977050b171b3f6b48175fea6e0565b7981059b486075e1026a9fb5"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-complex"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fbc387afefefd5e9e39493299f3069e14a140dd34dc19b4c1c1a8fddb6a790"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-derive"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c8b15b261814f992e33760b1fca9fe8b693d8a65299f20c9901688636cfb746"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "num-format"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bafe4179722c2894288ee77a9f044f02811c86af699344c498b0840c698a2465"
dependencies = [
 "arrayvec",
 "itoa 0.4.4",
]

[[package]]
name = "num-integer"
version = "0.1.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2cc698a63b549a70bc047073d2949cce27cd1c7b0a4a862d08a8031bc2801db"
dependencies = [
 "autocfg",
 "num-traits",
]

[[package]]
name = "num-iter"
version = "0.1.42"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2021c8337a54d21aca0d59a92577a029af9431cb59b909b03252b9c164fad59"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5b4d7360f362cfb50dde8143501e6940b22f644be75a4cc90b2d81968908138"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d41702bd167c2df5520b384281bc111a4b5efcf7fbc4c9c222c815b07e0a6a6a"
dependencies = [
 "autocfg",
 "num-bigint",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a64b1ec5cda2586e284722486d802acf1f7dbdc623e2bfc57e65ca1cd099290"
dependencies = [
 "autocfg",
]

[[package]]
name = "num_cpus"
version = "1.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19e64526ebdee182341572e50e9ad03965aa510cd94427a4549448f285e957a1"
dependencies = [
 "hermit-abi",
 "libc 0.2.132",
]

[[package]]
name = "object"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39f37e50073ccad23b6d09bcb5b263f4e76d3bb6038e4a3c08e52162ffa8abc2"
dependencies = [
 "memchr",
]

[[package]]
name = "once_cell"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86f0b0d4bf799edbc74508c1e8bf170ff5f41238e5f8225603ca7caaae2b7860"

[[package]]
name = "online_config"
version = "0.1.0"
dependencies = [
 "online_config_derive",
 "serde",
 "toml",
]

[[package]]
name = "online_config_derive"
version = "0.1.0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "oorandom"
version = "11.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ab1bc2a289d34bd04a330323ac98a1b4bc82c9d9fcb1e66b63caa84da26b575"

[[package]]
name = "opaque-debug"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "624a8340c38c1b80fd549087862da4ba43e08858af025b236e509b6649fc13d5"

[[package]]
name = "openssl"
version = "0.10.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "618febf65336490dfcf20b73f885f5651a0c89c64c2d4a8c3662585a70bf5bd0"
dependencies = [
 "bitflags",
 "cfg-if 1.0.0",
 "foreign-types",
 "libc 0.2.132",
 "once_cell",
 "openssl-macros",
 "openssl-sys",
]

[[package]]
name = "openssl-macros"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b501e44f11665960c7e7fcf062c7d96a14ade4aa98116c004b2e37b5be7d736c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "openssl-probe"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77af24da69f9d9341038eba93a073b1fdaaa1b788221b00a69bce9e762cb32de"

[[package]]
name = "openssl-src"
version = "111.20.0****.1o"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92892c4f87d56e376e469ace79f1128fdaded07646ddf73aa0be4706ff712dec"
dependencies = [
 "cc",
]

[[package]]
name = "openssl-sys"
version = "0.9.75"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5f9bd0c2710541a3cda73d6f9ac4f1b240de4ae261065d309dbe73d9dceb42f"
dependencies = [
 "autocfg",
 "cc",
 "libc 0.2.132",
 "openssl-src",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "ordered-float"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7940cf2ca942593318d07fcf2596cdca60a85c9e7fab408a5e21a4f9dcd40d87"
dependencies = [
 "num-traits",
]

[[package]]
name = "os_str_bytes"
version = "6.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e22443d1643a904602595ba1cd8f7d896afe56d26712531c5ff73a15b2fbf64"
dependencies = [
 "memchr",
]

[[package]]
name = "page_size"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eebde548fbbf1ea81a99b128872779c437752fb99f217c45245e1a61dcd9edcd"
dependencies = [
 "libc 0.2.132",
 "winapi 0.3.9",
]

[[package]]
name = "panic_hook"
version = "0.0.1"

[[package]]
name = "parking_lot"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d7744ac029df22dca6284efe4e898991d28e3085c706c972bcd7da4a27a15eb"
dependencies = [
 "instant",
 "lock_api",
 "parking_lot_core 0.8.3",
]

[[package]]
name = "parking_lot"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87f5ec2493a61ac0506c0f4199f99070cbe83857b0337006a30f3e6719b8ef58"
dependencies = [
 "lock_api",
 "parking_lot_core 0.9.1",
]

[[package]]
name = "parking_lot_core"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa7a782938e745763fe6907fc6ba86946d72f49fe7e21de074e08128a99fb018"
dependencies = [
 "cfg-if 1.0.0",
 "instant",
 "libc 0.2.132",
 "redox_syscall 0.2.11",
 "smallvec",
 "winapi 0.3.9",
]

[[package]]
name = "parking_lot_core"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28141e0cc4143da2443301914478dc976a61ffdb3f043058310c70df2fed8954"
dependencies = [
 "cfg-if 1.0.0",
 "libc 0.2.132",
 "redox_syscall 0.2.11",
 "smallvec",
 "windows-sys 0.32.0",
]

[[package]]
name = "parse-zoneinfo"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "089a398ccdcdd77b8c38909d5a1e4b67da1bc4c9dbfe6d5b536c828eddb779e5"
dependencies = [
 "regex",
]

[[package]]
name = "paste"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5d65c4d95931acda4498f675e332fcbdc9a06705cd07086c510e9b6009cd1c1"

[[package]]
name = "pd_client"
version = "0.1.0"
dependencies = [
 "collections",
 "error_code",
 "fail",
 "futures 0.3.15",
 "grpcio",
 "kvproto",
 "lazy_static",
 "log",
 "log_wrappers",
 "prometheus",
 "security",
 "semver 0.10.0",
 "serde",
 "slog",
 "slog-global",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
 "yatp",
]

[[package]]
name = "pdqselect"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ec91767ecc0a0bbe558ce8c9da33c068066c57ecc8bb8477ef8c1ad3ef77c27"

[[package]]
name = "peeking_take_while"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19b17cddbe7ec3f8bc800887bab5e717348c95ea2ca0b1bf0837fb964dc67099"

[[package]]
name = "percent-encoding"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4fd5641d01c8f18a23da7b6fe29298ff4b55afcccdf78973b24cf3175fee32e"

[[package]]
name = "perfcnt"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8f94885300e262ef461aa9fd1afbf7df3caf9e84e271a74925d1c6c8b24830f"
dependencies = [
 "bitflags",
 "byteorder",
 "libc 0.2.132",
 "mmap",
 "nom 4.2.3",
 "phf",
 "x86",
]

[[package]]
name = "pest"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10f4872ae94d7b90ae48754df22fd42ad52ce740b8f370b03da4835417403e53"
dependencies = [
 "ucd-trie",
]

[[package]]
name = "petgraph"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a13a2fa9d0b63e5f22328828741e523766fff0ee9e779316902290dff3f824f"
dependencies = [
 "fixedbitset",
 "indexmap",
]

[[package]]
name = "phf"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2ac8b67553a7ca9457ce0e526948cad581819238f4a9d1ea74545851fa24f37"
dependencies = [
 "phf_shared",
]

[[package]]
name = "phf_codegen"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "963adb11cf22ee65dfd401cf75577c1aa0eca58c0b97f9337d2da61d3e640503"
dependencies = [
 "phf_generator",
 "phf_shared",
]

[[package]]
name = "phf_generator"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d43f3220d96e0080cc9ea234978ccd80d904eafb17be31bb0f76daaea6493082"
dependencies = [
 "phf_shared",
 "rand 0.8.5",
]

[[package]]
name = "phf_shared"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a68318426de33640f02be62b4ae8eb1261be2efbc337b60c54d845bf4484e0d9"
dependencies = [
 "siphasher",
]

[[package]]
name = "pin-project"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78203e83c48cffbe01e4a2d35d566ca4de445d79a85372fc64e378bfc812a260"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "710faf75e1b33345361201d36d04e98ac1ed8909151a017ed384700836104c74"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "pin-project-lite"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0a7ae3ac2f1173085d398531c705756c94a4c56843785df85a60c1a0afac116"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "pkg-config"
version = "0.3.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72d5370d90f49f70bd033c3d75e87fc529fbfff9d6f7cccef07d6170079d91ea"

[[package]]
name = "plotters"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a3fd9ec30b9749ce28cd91f255d569591cdf937fe280c312143e3c4bad6f2a"
dependencies = [
 "num-traits",
 "plotters-backend",
 "plotters-svg",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "plotters-backend"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b07fffcddc1cb3a1de753caa4e4df03b79922ba43cf882acc1bdd7e8df9f4590"

[[package]]
name = "plotters-svg"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b38a02e23bd9604b842a812063aec4ef702b57989c37b655254bb61c471ad211"
dependencies = [
 "plotters-backend",
]

[[package]]
name = "pnet_base"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4df28acf2fcc77436dd2b91a9a0c2bb617f9ca5f2acefee1a4135058b9f9801f"

[[package]]
name = "pnet_datalink"
version = "0.23.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d27361d7578b410d0eb5fe815c2b2105b01ab770a7c738cb9a231457a809fcc7"
dependencies = [
 "ipnetwork",
 "libc 0.2.132",
 "pnet_base",
 "pnet_sys",
 "winapi 0.2.8",
]

[[package]]
name = "pnet_sys"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82f881a6d75ac98c5541db6144682d1773bb14c6fc50c6ebac7086c8f7f23c29"
dependencies = [
 "libc 0.2.132",
 "winapi 0.2.8",
 "ws2_32-sys",
]

[[package]]
name = "pprof"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e20150f965e0e4c925982b9356da71c84bcd56cb66ef4e894825837cbcf6613e"
dependencies = [
 "backtrace",
 "cfg-if 1.0.0",
 "findshlibs",
 "inferno",
 "libc 0.2.132",
 "log",
 "nix 0.24.1",
 "once_cell",
 "parking_lot 0.12.0",
 "protobuf",
 "protobuf-codegen-pure",
 "smallvec",
 "symbolic-demangle",
 "tempfile",
 "thiserror",
]

[[package]]
name = "ppv-lite86"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac74c624d6b2d21f425f752262f42188365d7b8ff1aff74c82e45136510a4857"

[[package]]
name = "prettyplease"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c142c0e46b57171fe0c528bee8c5b7569e80f0c17e377cd0e30ea57dbc11bb51"
dependencies = [
 "proc-macro2",
 "syn 1.0.103",
]

[[package]]
name = "proc-macro-error"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c"
dependencies = [
 "proc-macro-error-attr",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
 "version_check 0.9.4",
]

[[package]]
name = "proc-macro-error-attr"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869"
dependencies = [
 "proc-macro2",
 "quote",
 "version_check 0.9.4",
]

[[package]]
name = "proc-macro-hack"
version = "0.5.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbf0c48bc1d91375ae5c3cd81e3722dff1abcf81a30960240640d223f59fe0e5"

[[package]]
name = "proc-macro-nested"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "369a6ed065f249a159e06c45752c780bda2fb53c995718f9e484d08daa9eb42e"

[[package]]
name = "proc-macro2"
version = "1.0.95"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02b3e5e68a3a1a02aad3ec490a98007cbc13c37cbe84a3cd7b8e406d76e7f778"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "procfs"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0941606b9934e2d98a3677759a971756eb821f75764d0e0d26946d08e74d9104"
dependencies = [
 "bitflags",
 "byteorder",
 "hex 0.4.2",
 "lazy_static",
 "libc 0.2.132",
]

[[package]]
name = "procinfo"
version = "0.4.2"
source = "git+https://github.com/tikv/procinfo-rs?rev=6599eb9dca74229b2c1fcc44118bef7eff127128#6599eb9dca74229b2c1fcc44118bef7eff127128"
dependencies = [
 "byteorder",
 "libc 0.2.132",
 "nom 2.2.1",
 "rustc_version 0.2.3",
]

[[package]]
name = "profiler"
version = "0.0.1"
dependencies = [
 "callgrind",
 "gperftools",
 "lazy_static",
 "tikv_alloc",
 "valgrind_request",
]

[[package]]
name = "prometheus"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7f64969ffd5dd8f39bd57a68ac53c163a095ed9d0fb707146da1b27025a3504"
dependencies = [
 "cfg-if 1.0.0",
 "fnv",
 "lazy_static",
 "libc 0.2.132",
 "memchr",
 "parking_lot 0.11.1",
 "protobuf",
 "reqwest",
 "thiserror",
]

[[package]]
name = "prometheus-static-metric"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8f30cdb09c39930b8fa5e0f23cbb895ab3f766b187403a0ba0956fc1ef4f0e5"
dependencies = [
 "lazy_static",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "prost"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0841812012b2d4a6145fae9a6af1534873c32aa67fff26bd09f8fa42c83f95a"
dependencies = [
 "bytes",
 "prost-derive",
]

[[package]]
name = "prost-build"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d8b442418ea0822409d9e7d047cbf1e7e9e1760b172bf9982cf29d517c93511"
dependencies = [
 "bytes",
 "heck 0.4.0",
 "itertools",
 "lazy_static",
 "log",
 "multimap",
 "petgraph",
 "prettyplease",
 "prost",
 "prost-types",
 "regex",
 "syn 1.0.103",
 "tempfile",
 "which",
]

[[package]]
name = "prost-derive"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "164ae68b6587001ca506d3bf7f1000bfa248d0e1217b618108fba4ec1d0cc306"
dependencies = [
 "anyhow",
 "itertools",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "prost-types"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "747761bc3dc48f9a34553bf65605cf6cb6288ba219f3450b4275dbd81539551a"
dependencies = [
 "bytes",
 "prost",
]

[[package]]
name = "protobuf"
version = "2.8.0"
source = "git+https://gitee.com/JJZ921024/rust-protobuf.git#887f504fcbc275819502232a5cc53daae3c8912b"
dependencies = [
 "bytes",
 "heck 0.3.1",
 "hex 0.3.2",
]

[[package]]
name = "protobuf-build"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b2be70fa994657539e3c872cc54363c9bf28b0d7a7f774df70e9fd760df3bc4"
dependencies = [
 "bitflags",
 "grpcio-compiler",
 "protobuf",
 "protobuf-codegen",
 "regex",
]

[[package]]
name = "protobuf-codegen"
version = "2.8.0"
source = "git+https://gitee.com/JJZ921024/rust-protobuf.git#887f504fcbc275819502232a5cc53daae3c8912b"
dependencies = [
 "heck 0.3.1",
 "protobuf",
]

[[package]]
name = "protobuf-codegen-pure"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00993dc5fbbfcf9d8a005f6b6c29fd29fd6d86deba3ae3f41fd20c624c414616"
dependencies = [
 "protobuf",
 "protobuf-codegen",
]

[[package]]
name = "quick-xml"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8533f14c8382aaad0d592c812ac3b826162128b65662331e1127b45c3d18536b"
dependencies = [
 "memchr",
]

[[package]]
name = "quote"
version = "1.0.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1885c039570dc00dcb4ff087a89e185fd56bae234ddc7f056a945bf36467248d"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "raft"
version = "0.7.0"
source = "git+https://github.com/tikv/raft-rs?branch=master#2357cb22760719bcd107a90d1e64ef505bdb1e15"
dependencies = [
 "bytes",
 "fxhash",
 "getset",
 "protobuf",
 "raft-proto",
 "rand 0.8.5",
 "slog",
 "thiserror",
]

[[package]]
name = "raft-engine"
version = "0.3.0"
source = "git+https://github.com/tikv/raft-engine.git#82f6da7b8dff1856483e8e72a59dda903fb2499b"
dependencies = [
 "byteorder",
 "crc32fast",
 "crossbeam",
 "fail",
 "fs2",
 "hashbrown 0.12.0",
 "hex 0.4.2",
 "if_chain",
 "lazy_static",
 "libc 0.2.132",
 "log",
 "lz4-sys",
 "memmap2",
 "nix 0.25.0",
 "num-derive",
 "num-traits",
 "parking_lot 0.12.0",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "rayon",
 "rhai",
 "scopeguard",
 "serde",
 "serde_repr",
 "strum 0.24.1",
 "thiserror",
]

[[package]]
name = "raft-engine-ctl"
version = "0.3.0"
source = "git+https://github.com/tikv/raft-engine.git#82f6da7b8dff1856483e8e72a59dda903fb2499b"
dependencies = [
 "clap 3.1.6",
 "env_logger",
 "raft-engine",
]

[[package]]
name = "raft-proto"
version = "0.7.0"
source = "git+https://github.com/tikv/raft-rs?branch=master#2357cb22760719bcd107a90d1e64ef505bdb1e15"
dependencies = [
 "bytes",
 "protobuf",
 "protobuf-build",
]

[[package]]
name = "raft_log_engine"
version = "0.0.1"
dependencies = [
 "encryption",
 "engine_traits",
 "file_system",
 "kvproto",
 "lazy_static",
 "num_cpus",
 "online_config",
 "protobuf",
 "raft",
 "raft-engine",
 "serde",
 "slog",
 "slog-global",
 "tikv_util",
 "time",
 "tracker",
]

[[package]]
name = "raftstore"
version = "0.0.1"
dependencies = [
 "batch-system",
 "bitflags",
 "byteorder",
 "bytes",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crc32fast",
 "crossbeam",
 "derivative",
 "encryption",
 "encryption_export",
 "engine_panic",
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "error_code",
 "fail",
 "file_system",
 "fs2",
 "futures 0.3.15",
 "futures-util",
 "getset",
 "grpcio-health",
 "into_other",
 "itertools",
 "keys",
 "kvproto",
 "lazy_static",
 "log",
 "log_wrappers",
 "memory_trace_macros",
 "online_config",
 "openssl",
 "ordered-float",
 "panic_hook",
 "parking_lot 0.12.0",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "raft-proto",
 "rand 0.8.5",
 "resource_metering",
 "serde",
 "serde_with",
 "slog",
 "slog-global",
 "smallvec",
 "sst_importer",
 "tempfile",
 "test_sst_importer",
 "thiserror",
 "tidb_query_datatype",
 "tikv_alloc",
 "tikv_util",
 "time",
 "tokio",
 "tracker",
 "txn_types",
 "uuid 0.8.2",
 "yatp",
]

[[package]]
name = "raftstore-v2"
version = "0.1.0"
dependencies = [
 "batch-system",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "engine_test",
 "engine_traits",
 "error_code",
 "fail",
 "file_system",
 "fs2",
 "futures 0.3.15",
 "keys",
 "kvproto",
 "log_wrappers",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raft-proto",
 "raftstore",
 "resource_metering",
 "slog",
 "slog-global",
 "smallvec",
 "tempfile",
 "test_pd",
 "test_util",
 "tikv_util",
 "time",
 "tracker",
 "txn_types",
 "yatp",
]

[[package]]
name = "rand"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "552840b97013b1a26992c11eac34bdd778e464601a4c2054b5f0bff7c6761293"
dependencies = [
 "fuchsia-cprng",
 "libc 0.2.132",
 "rand_core 0.3.1",
 "rdrand",
 "winapi 0.3.9",
]

[[package]]
name = "rand"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a6b1679d49b24bbfe0c803429aa1874472f50d9b363131f0e89fc356b544d03"
dependencies = [
 "getrandom 0.1.12",
 "libc 0.2.132",
 "rand_chacha 0.2.1",
 "rand_core 0.5.1",
 "rand_hc",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc 0.2.132",
 "rand_chacha 0.3.0",
 "rand_core 0.6.2",
]

[[package]]
name = "rand_chacha"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03a2a90da8c7523f554344f921aa97283eadf6ac484a6d2a7d0212fa7f8d6853"
dependencies = [
 "c2-chacha",
 "rand_core 0.5.1",
]

[[package]]
name = "rand_chacha"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e12735cf05c9e10bf21534da50a147b924d555dc7a547c42e6bb2d5b6017ae0d"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.2",
]

[[package]]
name = "rand_core"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a6fdeb83b075e8266dcc8762c22776f6877a63111121f5f8c7411e5be7eed4b"
dependencies = [
 "rand_core 0.4.2",
]

[[package]]
name = "rand_core"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c33a3c44ca05fa6f1807d8e6743f3824e8509beca625669633be0acbdf509dc"

[[package]]
name = "rand_core"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90bde5296fc891b0cef12a6d03ddccc162ce7b2aff54160af9338f8d40df6d19"
dependencies = [
 "getrandom 0.1.12",
]

[[package]]
name = "rand_core"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34cf66eb183df1c5876e2dcf6b13d57340741e8dc255b48e40a26de954d06ae7"
dependencies = [
 "getrandom 0.2.3",
]

[[package]]
name = "rand_hc"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3129af7b92a17112d59ad498c6f81eaf463253766b90396d39ea7a39d6613c"
dependencies = [
 "rand_core 0.5.1",
]

[[package]]
name = "rand_isaac"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fac4373cd91b4f55722c553fb0f286edbb81ef3ff6eec7b99d1898a4110a0b28"
dependencies = [
 "rand_core 0.6.2",
]

[[package]]
name = "rand_xorshift"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d25bf25ec5ae4a3f1b92f929810509a2f53d7dca2f50b794ff57e3face536c8f"
dependencies = [
 "rand_core 0.6.2",
]

[[package]]
name = "raw-cpuid"
version = "10.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "929f54e29691d4e6a9cc558479de70db7aa3d98cd6fe7ab86d7507aa2886b9d2"
dependencies = [
 "bitflags",
]

[[package]]
name = "rayon"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b0d8e0819fadc20c74ea8373106ead0600e3a67ef1fe8da56e39b9ae7275674"
dependencies = [
 "autocfg",
 "crossbeam-deque",
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ab346ac5921dc62ffa9f89b7a773907511cdfa5490c572ae9be1be33e8afa4a"
dependencies = [
 "crossbeam-channel",
 "crossbeam-deque",
 "crossbeam-utils 0.8.8",
 "lazy_static",
 "num_cpus",
]

[[package]]
name = "rdrand"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "678054eb77286b51581ba43620cc911abf02758c91f93f479767aed0f90458b2"
dependencies = [
 "rand_core 0.3.1",
]

[[package]]
name = "redox_syscall"
version = "0.1.56"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2439c63f3f6139d1b57529d16bc3b8bb855230c8efcc5d3a896c8bea7c3b1e84"

[[package]]
name = "redox_syscall"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8380fe0152551244f0747b1bf41737e0f8a74f97a14ccefd1148187271634f3c"
dependencies = [
 "bitflags",
]

[[package]]
name = "redox_users"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "528532f3d801c87aec9def2add9ca802fe569e44a544afe633765267840abe64"
dependencies = [
 "getrandom 0.2.3",
 "redox_syscall 0.2.11",
]

[[package]]
name = "regex"
version = "1.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d83f127d94bdbcda4c8cc2e50f6f84f4b611f69c902699ca385a39c3a75f9ff1"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax",
]

[[package]]
name = "regex-automata"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92b73c2a1770c255c240eaa4ee600df1704a38dc3feaa6e949e7fcd4f8dc09f9"
dependencies = [
 "byteorder",
]

[[package]]
name = "regex-syntax"
version = "0.6.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49b3de9ec5dc0a3417da371aab17d729997c15010e7fd24ff707773a33bddb64"

[[package]]
name = "remove_dir_all"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a83fa3702a688b9359eccba92d153ac33fd2e8462f9e0e3fdf155239ea7792e"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "reqwest"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0460542b551950620a3648c6aa23318ac6b3cd779114bd873209e6e8b5eb1c34"
dependencies = [
 "base64",
 "bytes",
 "encoding_rs 0.8.29 (registry+https://github.com/rust-lang/crates.io-index)",
 "futures-core",
 "futures-util",
 "http",
 "http-body",
 "hyper",
 "hyper-tls",
 "ipnet",
 "js-sys",
 "lazy_static",
 "log",
 "mime",
 "native-tls",
 "percent-encoding",
 "pin-project-lite",
 "serde",
 "serde_urlencoded",
 "tokio",
 "tokio-native-tls",
 "url",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "winreg",
]

[[package]]
name = "resolved_ts"
version = "0.0.1"
dependencies = [
 "collections",
 "concurrency_manager",
 "crossbeam",
 "engine_rocks",
 "engine_traits",
 "fail",
 "futures 0.3.15",
 "grpcio",
 "hex 0.4.2",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "panic_hook",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raftstore",
 "security",
 "slog",
 "slog-global",
 "tempfile",
 "test_raftstore",
 "test_sst_importer",
 "test_util",
 "thiserror",
 "tikv",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "resource_metering"
version = "0.0.1"
dependencies = [
 "collections",
 "crossbeam",
 "futures 0.3.15",
 "grpcio",
 "kvproto",
 "lazy_static",
 "libc 0.2.132",
 "log",
 "online_config",
 "pdqselect",
 "pin-project",
 "procinfo",
 "prometheus",
 "rand 0.8.5",
 "serde",
 "slog",
 "slog-global",
 "tikv_util",
]

[[package]]
name = "rev_lines"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18eb52b6664d331053136fcac7e4883bdc6f5fc04a6aab3b0f75eafb80ab88b3"

[[package]]
name = "rgb"
version = "0.8.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e74fdc210d8f24a7dbfedc13b04ba5764f5232754ccebfdf5fff1bad791ccbc6"
dependencies = [
 "bytemuck",
]

[[package]]
name = "rhai"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f06953bb8b9e4307cb7ccc0d9d018e2ddd25a30d32831f631ce4fe8f17671f7"
dependencies = [
 "ahash",
 "bitflags",
 "instant",
 "num-traits",
 "rhai_codegen",
 "smallvec",
 "smartstring",
]

[[package]]
name = "rhai_codegen"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75a39bc2aa9258b282ee5518dac493491a9c4c11a6d7361b9d2644c922fc6488"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "rocksdb"
version = "0.3.0"
source = "git+https://github.com/tikv/rust-rocksdb.git#bd07e9e598db63574cf06edaeea3c4687eadff59"
dependencies = [
 "libc 0.2.132",
 "librocksdb_sys",
]

[[package]]
name = "rusoto_core"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=gh1482-s3-addr-styles#0d6df7b119c4e757daaa715f261c3150c7ae0a3b"
dependencies = [
 "async-trait",
 "base64",
 "bytes",
 "crc32fast",
 "futures 0.3.15",
 "http",
 "hyper",
 "hyper-tls",
 "lazy_static",
 "log",
 "rusoto_credential",
 "rusoto_signature",
 "rustc_version 0.3.3",
 "serde",
 "serde_json",
 "tokio",
 "xml-rs",
]

[[package]]
name = "rusoto_credential"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=gh1482-s3-addr-styles#0d6df7b119c4e757daaa715f261c3150c7ae0a3b"
dependencies = [
 "async-trait",
 "chrono",
 "dirs-next",
 "futures 0.3.15",
 "hyper",
 "serde",
 "serde_json",
 "shlex 0.1.1",
 "tokio",
 "zeroize",
]

[[package]]
name = "rusoto_signature"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=gh1482-s3-addr-styles#0d6df7b119c4e757daaa715f261c3150c7ae0a3b"
dependencies = [
 "base64",
 "bytes",
 "chrono",
 "digest",
 "futures 0.3.15",
 "hex 0.4.2",
 "hmac",
 "http",
 "hyper",
 "log",
 "md-5",
 "percent-encoding",
 "pin-project-lite",
 "rusoto_credential",
 "rustc_version 0.3.3",
 "serde",
 "sha2",
 "tokio",
]

[[package]]
name = "rust-ini"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c96a7d6722944454c68ff2ba2a252a4e9b0635c03dd510fdf482a2c8981cbf2"
dependencies = [
 "multimap",
]

[[package]]
name = "rustc-demangle"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c691c0e608126e00913e33f0ccf3727d5fc84573623b8d65b2df340b5201783"

[[package]]
name = "rustc-hash"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7540fc8b0c49f096ee9c961cda096467dce8084bec6bdca2fc83895fd9b28cb8"
dependencies = [
 "byteorder",
]

[[package]]
name = "rustc_version"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "138e3e0acb6c9fb258b19b67cb8abd63c00679d2851805ea151465464fe9030a"
dependencies = [
 "semver 0.9.0",
]

[[package]]
name = "rustc_version"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0dfe2087c51c460008730de8b57e6a320782fbfb312e1f4d520e6c6fae155ee"
dependencies = [
 "semver 0.11.0",
]

[[package]]
name = "rustversion"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb5d2a036dc6d2d8fd16fde3498b04306e29bd193bf306a57427019b823d5acd"

[[package]]
name = "ryu"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed3d612bc64430efeb3f7ee6ef26d590dce0c43249217bddc62112540c7941e1"

[[package]]
name = "safemem"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2b08423011dae9a5ca23f07cf57dac3857f5c885d352b76f6d95f4aea9434d0"

[[package]]
name = "same-file"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "585e8ddcedc187886a30fa705c47985c3fa88d06624095856b36ca0b82ff4421"
dependencies = [
 "winapi-util",
]

[[package]]
name = "schannel"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87f550b06b6cba9c8b8be3ee73f391990116bf527450d2556e9b9ce263b9a021"
dependencies = [
 "lazy_static",
 "winapi 0.3.9",
]

[[package]]
name = "scopeguard"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d29ab0c6d3fc0ee92fe66e2d99f700eab17a8d57d1c1d3b748380fb20baa78cd"

[[package]]
name = "seahash"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c107b6f4780854c8b126e228ea8869f4d7b71260f962fefb57b996b8959ba6b"

[[package]]
name = "security"
version = "0.0.1"
dependencies = [
 "collections",
 "encryption",
 "grpcio",
 "serde",
 "serde_json",
 "tempfile",
 "tikv_util",
]

[[package]]
name = "security-framework"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3670b1d2fdf6084d192bc71ead7aabe6c06aa2ea3fbd9cc3ac111fa5c2b1bd84"
dependencies = [
 "bitflags",
 "core-foundation",
 "core-foundation-sys",
 "libc 0.2.132",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3676258fd3cfe2c9a0ec99ce3038798d847ce3e4bb17746373eb9f0f1ac16339"
dependencies = [
 "core-foundation-sys",
 "libc 0.2.132",
]

[[package]]
name = "semver"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d7eb9ef2c18661902cc47e535f9bc51b78acd254da71d375c2f6720d9a40403"
dependencies = [
 "semver-parser 0.7.0",
 "serde",
]

[[package]]
name = "semver"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "394cec28fa623e00903caf7ba4fa6fb9a0e260280bb8cdbbba029611108a0190"
dependencies = [
 "semver-parser 0.7.0",
]

[[package]]
name = "semver"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f301af10236f6df4160f7c3f04eec6dbc70ace82d23326abad5edee88801c6b6"
dependencies = [
 "semver-parser 0.10.2",
]

[[package]]
name = "semver"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "568a8e6258aa33c13358f81fd834adb854c6f7c9468520910a9b1e8fac068012"

[[package]]
name = "semver-parser"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "388a1df253eca08550bef6c72392cfe7c30914bf41df5269b68cbd6ff8f570a3"

[[package]]
name = "semver-parser"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0bef5b7f9e0df16536d3961cfb6e84331c065b4066afb39768d0e319411f7"
dependencies = [
 "pest",
]

[[package]]
name = "serde"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f0e2c6ed6606019b4e29e69dbaba95b11854410e5347d525002456dbbb786b6"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde_cbor"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e18acfa2f90e8b735b2836ab8d538de304cbb6729a7360729ea5a895d15a622"
dependencies = [
 "half",
 "serde",
]

[[package]]
name = "serde_derive"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b0276cf7f2c73365f7157c8123c21cd9a50fbbd844757af28ca1f5925fc2a00"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.102",
]

[[package]]
name = "serde_ignored"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c2c7d39d14f2f2ea82239de71594782f186fd03501ac81f0ce08e674819ff2f"
dependencies = [
 "serde",
]

[[package]]
name = "serde_json"
version = "1.0.64"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "799e97dc9fdae36a5c8b8f2cae9ce2ee9fdce2058c57a93e6099d919fd982f79"
dependencies = [
 "indexmap",
 "itoa 0.4.4",
 "ryu",
 "serde",
]

[[package]]
name = "serde_repr"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fe39d9fbb0ebf5eb2c7cb7e2a47e4f462fad1379f1166b8ae49ad9eae89a7ca"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "serde_urlencoded"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edfa57a7f8d9c1d260a549e7224100f6c43d43f9103e06dd8b4095a9b2b43ce9"
dependencies = [
 "form_urlencoded",
 "itoa 0.4.4",
 "ryu",
 "serde",
]

[[package]]
name = "serde_with"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89d3d595d64120bbbc70b7f6d5ae63298b62a3d9f373ec2f56acf5365ca8a444"
dependencies = [
 "serde",
 "serde_with_macros",
]

[[package]]
name = "serde_with_macros"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4070d2c9b9d258465ad1d82aabb985b84cd9a3afa94da25ece5a9938ba5f1606"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "server"
version = "0.0.1"
dependencies = [
 "api_version",
 "backup",
 "backup-stream",
 "causal_ts",
 "cdc",
 "chrono",
 "clap 2.33.0",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption",
 "encryption_export",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_traits",
 "error_code",
 "file_system",
 "fs2",
 "futures 0.3.15",
 "grpcio",
 "grpcio-health",
 "hex 0.4.2",
 "keys",
 "kvproto",
 "libc 0.2.132",
 "log",
 "log_wrappers",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raft_log_engine",
 "raftstore",
 "rand 0.8.5",
 "resolved_ts",
 "resource_metering",
 "security",
 "serde_json",
 "signal-hook",
 "slog",
 "slog-global",
 "snap_recovery",
 "tempfile",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "toml",
 "txn_types",
 "yatp",
]

[[package]]
name = "sha2"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2933378ddfeda7ea26f48c555bdad8bb446bf8a3d17832dc83e380d444cfb8c1"
dependencies = [
 "block-buffer",
 "cfg-if 0.1.10",
 "cpuid-bool",
 "digest",
 "opaque-debug",
]

[[package]]
name = "shlex"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fdf1b9db47230893d76faad238fd6097fd6d6a9245cd7a4d90dbd639536bbd2"

[[package]]
name = "shlex"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43b2853a4d09f215c24cc5489c992ce46052d359b5109343cbafbf26bc62f8a3"

[[package]]
name = "signal-hook"
version = "0.3.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a253b5e89e2698464fc26b545c9edceb338e18a89effeeecfea192c3025be29d"
dependencies = [
 "libc 0.2.132",
 "signal-hook-registry",
]

[[package]]
name = "signal-hook-registry"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e51e73328dc4ac0c7ccbda3a494dfa03df1de2f46018127f60c693f2648455b0"
dependencies = [
 "libc 0.2.132",
]

[[package]]
name = "siphasher"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa8f3741c7372e75519bd9346068370c9cdaabcc1f9599cbcf2a2719352286b7"

[[package]]
name = "slab"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c111b5bd5695e56cffe5129854aa230b39c93a305372fdbb2668ca2394eea9f8"

[[package]]
name = "slog"
version = "2.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cc9c640a4adbfbcc11ffb95efe5aa7af7309e002adab54b185507dbf2377b99"

[[package]]
name = "slog-async"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c60813879f820c85dbc4eabf3269befe374591289019775898d56a81a804fbdc"
dependencies = [
 "crossbeam-channel",
 "slog",
 "take_mut",
 "thread_local",
]

[[package]]
name = "slog-global"
version = "0.1.0"
source = "git+https://github.com/breeswish/slog-global.git?rev=d592f88e4dbba5eb439998463054f1a44fbf17b9#d592f88e4dbba5eb439998463054f1a44fbf17b9"
dependencies = [
 "arc-swap",
 "lazy_static",
 "log",
 "slog",
]

[[package]]
name = "slog-json"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ddc0d2aff1f8f325ef660d9a0eb6e6dcd20b30b3f581a5897f58bf42d061c37a"
dependencies = [
 "chrono",
 "serde",
 "serde_json",
 "slog",
]

[[package]]
name = "slog-term"
version = "2.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3668dd2252f4381d64de0c79e6c8dc6bd509d1cab3535b35a3fc9bafd1241d5"
dependencies = [
 "atty",
 "chrono",
 "slog",
 "term",
 "thread_local",
]

[[package]]
name = "slog_derive"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a945ec7f7ce853e89ffa36be1e27dce9a43e82ff9093bf3461c30d5da74ed11b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "smallvec"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2dd574626839106c320a323308629dcb1acfc96e32a8cba364ddc61ac23ee83"

[[package]]
name = "smartstring"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fb72c633efbaa2dd666986505016c32c3044395ceaf881518399d2f4127ee29"
dependencies = [
 "autocfg",
 "static_assertions",
 "version_check 0.9.4",
]

[[package]]
name = "snap_recovery"
version = "0.1.0"
dependencies = [
 "chrono",
 "encryption",
 "encryption_export",
 "engine_rocks",
 "engine_traits",
 "futures 0.3.15",
 "grpcio",
 "keys",
 "kvproto",
 "log",
 "pd_client",
 "protobuf",
 "raft_log_engine",
 "raftstore",
 "slog",
 "slog-global",
 "structopt",
 "tempfile",
 "thiserror",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "toml",
 "txn_types",
]

[[package]]
name = "snappy-sys"
version = "0.1.0"
source = "git+https://github.com/busyjay/rust-snappy.git?branch=static-link#8c12738bad811397600455d6982aff754ea2ac44"
dependencies = [
 "cmake",
 "libc 0.2.132",
 "pkg-config",
]

[[package]]
name = "snmalloc-rs"
version = "0.2.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f5a6194d59b08fc87381e7c8a04ab4ab9967282b00f409bb742e08f3514ed0b"
dependencies = [
 "snmalloc-sys",
]

[[package]]
name = "snmalloc-sys"
version = "0.2.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9518813a25ab2704a6df4968f609aa6949705409b6a854dcc87018d12961cbc8"
dependencies = [
 "cmake",
]

[[package]]
name = "socket2"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02e2d2db9033d13a1567121ddd7a095ee144db4e1ca1b1bda3419bc0da294ebd"
dependencies = [
 "libc 0.2.132",
 "winapi 0.3.9",
]

[[package]]
name = "sst_importer"
version = "0.1.0"
dependencies = [
 "api_version",
 "crc32fast",
 "dashmap",
 "encryption",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "external_storage_export",
 "file_system",
 "futures 0.3.15",
 "futures-util",
 "grpcio",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "openssl",
 "prometheus",
 "rand 0.8.5",
 "serde",
 "slog",
 "slog-global",
 "tempfile",
 "test_sst_importer",
 "test_util",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "txn_types",
 "uuid 0.8.2",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "str_stack"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9091b6114800a5f2141aee1d1b9d6ca3592ac062dc5decb3764ec5895a47b4eb"

[[package]]
name = "strsim"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ea5119cdb4c55b55d432abb513a0429384878c15dde60cc77b1c99de1a95a6a"

[[package]]
name = "strsim"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "032c03039aae92b350aad2e3779c352e104d919cb192ba2fabbd7b831ce4f0f6"

[[package]]
name = "strsim"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73473c0e59e6d5812c5dfe2a064a6444949f089e20eec9a2e5506596494e4623"

[[package]]
name = "structopt"
version = "0.3.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "126d630294ec449fae0b16f964e35bf3c74f940da9dca17ee9b905f7b3112eb8"
dependencies = [
 "clap 2.33.0",
 "lazy_static",
 "structopt-derive",
]

[[package]]
name = "structopt-derive"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65e51c492f9e23a220534971ff5afc14037289de430e3c83f9daf6a1b6ae91e8"
dependencies = [
 "heck 0.3.1",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "strum"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7318c509b5ba57f18533982607f24070a55d353e90d4cae30c467cdb2ad5ac5c"
dependencies = [
 "strum_macros 0.20.1",
]

[[package]]
name = "strum"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "063e6045c0e62079840579a7e47a355ae92f60eb74daaf156fb1e84ba164e63f"
dependencies = [
 "strum_macros 0.24.2",
]

[[package]]
name = "strum_macros"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee8bc6b87a5112aeeab1f4a9f7ab634fe6cbefc4850006df31267f4cfb9e3149"
dependencies = [
 "heck 0.3.1",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "strum_macros"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4faebde00e8ff94316c01800f9054fd2ba77d30d9e922541913051d1d978918b"
dependencies = [
 "heck 0.4.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 1.0.103",
]

[[package]]
name = "subtle"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "343f3f510c2915908f155e94f17220b19ccfacf2a64a2a5d8004f2c3e311e7fd"

[[package]]
name = "symbolic-common"
version = "10.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac457d054f793cedfde6f32d21d692b8351cfec9084fefd0470c0373f6d799bc"
dependencies = [
 "debugid",
 "memmap2",
 "stable_deref_trait",
 "uuid 1.2.1",
]

[[package]]
name = "symbolic-demangle"
version = "10.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48808b846eef84e0ac06365dc620f028ae632355e5dcffc007bf1b2bf5eab17b"
dependencies = [
 "rustc-demangle",
 "symbolic-common",
]

[[package]]
name = "syn"
version = "1.0.103"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a864042229133ada95abf3b54fdc62ef5ccabe9515b64717bcb9a1919e59445d"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.102"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6397daf94fa90f058bd0fd88429dd9e5738999cca8d701813c80723add80462"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "sync_wrapper"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20518fe4a4c9acf048008599e464deb21beeae3d3578418951a189c235a7a9a8"

[[package]]
name = "sysinfo"
version = "0.16.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c280c91abd1aed2e36be1bc8f56fbc7a2acbb2b58fbcac9641510179cc72dd9"
dependencies = [
 "cfg-if 1.0.0",
 "core-foundation-sys",
 "doc-comment",
 "libc 0.2.132",
 "ntapi",
 "once_cell",
 "rayon",
 "winapi 0.3.9",
]

[[package]]
name = "take_mut"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f764005d11ee5f36500a149ace24e00e3da98b0158b3e2d53a7495660d3f4d60"

[[package]]
name = "tcmalloc"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "375205113d84a1c5eeed67beaa0ce08e41be1a9d5acc3425ad2381fddd9d819b"
dependencies = [
 "tcmalloc-sys",
]

[[package]]
name = "tcmalloc-sys"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b7ad73e635dd232c2c2106d59269f59a61de421cc6b95252d2d932094ff1f40"

[[package]]
name = "tempdir"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15f2b5fb00ccdf689e0149d1b1b3c03fead81c2b37735d812fa8bddbbf41b6d8"
dependencies = [
 "rand 0.4.6",
 "remove_dir_all",
]

[[package]]
name = "tempfile"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dac1c663cfc93810f88aed9b8941d48cabf856a1b111c29a40439018d870eb22"
dependencies = [
 "cfg-if 1.0.0",
 "libc 0.2.132",
 "rand 0.8.5",
 "redox_syscall 0.2.11",
 "remove_dir_all",
 "winapi 0.3.9",
]

[[package]]
name = "term"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c59df8ac95d96ff9bede18eb7300b0fda5e5d8d90960e76f8e14ae765eedbf1f"
dependencies = [
 "dirs-next",
 "rustversion",
 "winapi 0.3.9",
]

[[package]]
name = "termcolor"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bab24d30b911b2376f3a13cc2cd443142f0c81dda04c118693e35b3835757755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "test_backup"
version = "0.0.1"
dependencies = [
 "api_version",
 "backup",
 "collections",
 "concurrency_manager",
 "crc64fast",
 "engine_traits",
 "external_storage_export",
 "file_system",
 "futures 0.3.15",
 "futures-executor",
 "futures-util",
 "grpcio",
 "kvproto",
 "protobuf",
 "rand 0.8.5",
 "tempfile",
 "test_raftstore",
 "tidb_query_common",
 "tikv",
 "tikv_util",
 "txn_types",
]

[[package]]
name = "test_coprocessor"
version = "0.0.1"
dependencies = [
 "api_version",
 "collections",
 "concurrency_manager",
 "engine_rocks",
 "futures 0.3.15",
 "kvproto",
 "protobuf",
 "resource_metering",
 "test_storage",
 "tidb_query_common",
 "tidb_query_datatype",
 "tikv",
 "tikv_util",
 "tipb",
 "txn_types",
]

[[package]]
name = "test_pd"
version = "0.0.1"
dependencies = [
 "collections",
 "fail",
 "futures 0.3.15",
 "grpcio",
 "kvproto",
 "pd_client",
 "security",
 "slog",
 "slog-global",
 "tikv_util",
]

[[package]]
name = "test_pd_client"
version = "0.0.1"
dependencies = [
 "collections",
 "fail",
 "futures 0.3.15",
 "grpcio",
 "keys",
 "kvproto",
 "log_wrappers",
 "pd_client",
 "raft",
 "slog",
 "slog-global",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
]

[[package]]
name = "test_raftstore"
version = "0.0.1"
dependencies = [
 "api_version",
 "backtrace",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption_export",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_test",
 "engine_traits",
 "fail",
 "file_system",
 "futures 0.3.15",
 "grpcio",
 "grpcio-health",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "pd_client",
 "protobuf",
 "raft",
 "raftstore",
 "rand 0.8.5",
 "resolved_ts",
 "resource_metering",
 "security",
 "server",
 "slog",
 "slog-global",
 "tempfile",
 "test_pd_client",
 "test_util",
 "tikv",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
]

[[package]]
name = "test_sst_importer"
version = "0.1.0"
dependencies = [
 "crc32fast",
 "engine_rocks",
 "engine_traits",
 "keys",
 "kvproto",
 "uuid 0.8.2",
]

[[package]]
name = "test_storage"
version = "0.0.1"
dependencies = [
 "api_version",
 "collections",
 "futures 0.3.15",
 "kvproto",
 "pd_client",
 "raftstore",
 "test_raftstore",
 "tikv",
 "tikv_util",
 "tracker",
 "txn_types",
]

[[package]]
name = "test_util"
version = "0.0.1"
dependencies = [
 "backtrace",
 "collections",
 "encryption_export",
 "fail",
 "grpcio",
 "kvproto",
 "rand 0.8.5",
 "rand_isaac",
 "security",
 "slog",
 "slog-global",
 "tempfile",
 "tikv_util",
 "time",
]

[[package]]
name = "tests"
version = "0.0.1"
dependencies = [
 "api_version",
 "arrow",
 "async-trait",
 "batch-system",
 "byteorder",
 "causal_ts",
 "cdc",
 "collections",
 "concurrency_manager",
 "crc64fast",
 "criterion",
 "criterion-cpu-time",
 "criterion-perf-events",
 "crossbeam",
 "encryption",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_test",
 "engine_traits",
 "error_code",
 "external_storage_export",
 "fail",
 "file_system",
 "futures 0.3.15",
 "grpcio",
 "grpcio-health",
 "hyper",
 "keys",
 "kvproto",
 "libc 0.2.132",
 "log_wrappers",
 "more-asserts",
 "online_config",
 "panic_hook",
 "paste",
 "pd_client",
 "perfcnt",
 "procinfo",
 "profiler",
 "protobuf",
 "raft",
 "raft_log_engine",
 "raftstore",
 "rand 0.8.5",
 "rand_xorshift",
 "resource_metering",
 "security",
 "serde_json",
 "slog",
 "slog-global",
 "sst_importer",
 "tempfile",
 "test_backup",
 "test_coprocessor",
 "test_pd",
 "test_pd_client",
 "test_raftstore",
 "test_sst_importer",
 "test_storage",
 "test_util",
 "tidb_query_aggr",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_executors",
 "tidb_query_expr",
 "tikv",
 "tikv_kv",
 "tikv_util",
 "time",
 "tipb",
 "tipb_helper",
 "tokio",
 "toml",
 "txn_types",
 "uuid 0.8.2",
]

[[package]]
name = "textwrap"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d326610f408c7a4eb6f51c37c330e496b08506c9457c9d34287ecc38809fb060"
dependencies = [
 "unicode-width",
]

[[package]]
name = "textwrap"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1141d4d61095b28419e22cb0bbf02755f5e54e0526f97f1e3d1d160e60885fb"

[[package]]
name = "thiserror"
version = "1.0.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "854babe52e4df1653706b98fcfc05843010039b406875930a70e4d9644e5c417"
dependencies = [
 "thiserror-impl",
]

[[package]]
name = "thiserror-impl"
version = "1.0.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa32fd3f627f367fe16f893e2597ae3c05020f8bba2666a4e6ea73d377e5714b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "thread_local"
version = "1.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5516c27b78311c50bf42c071425c560ac799b11c30b31f87e3081965fe5e0180"
dependencies = [
 "once_cell",
]

[[package]]
name = "tidb_query_aggr"
version = "0.0.1"
dependencies = [
 "match-template",
 "panic_hook",
 "tidb_query_codegen",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_expr",
 "tikv_util",
 "tipb",
 "tipb_helper",
]

[[package]]
name = "tidb_query_codegen"
version = "0.0.1"
dependencies = [
 "darling",
 "heck 0.3.1",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "tidb_query_common"
version = "0.0.1"
dependencies = [
 "anyhow",
 "async-trait",
 "byteorder",
 "derive_more",
 "error_code",
 "futures 0.3.15",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "prometheus",
 "prometheus-static-metric",
 "serde_json",
 "thiserror",
 "tikv_util",
 "time",
 "yatp",
]

[[package]]
name = "tidb_query_datatype"
version = "0.0.1"
dependencies = [
 "base64",
 "bitfield",
 "bitflags",
 "boolinator",
 "bstr",
 "chrono",
 "chrono-tz",
 "codec",
 "collections",
 "encoding_rs 0.8.29 (git+https://github.com/xiongjiwei/encoding_rs.git?rev=68e0bc5a72a37a78228d80cd98047326559cf43c)",
 "error_code",
 "hex 0.4.2",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "match-template",
 "nom 7.1.0",
 "num 0.3.0",
 "num-derive",
 "num-traits",
 "ordered-float",
 "protobuf",
 "regex",
 "serde",
 "serde_json",
 "slog",
 "slog-global",
 "static_assertions",
 "thiserror",
 "tidb_query_common",
 "tikv_alloc",
 "tikv_util",
 "tipb",
]

[[package]]
name = "tidb_query_executors"
version = "0.0.1"
dependencies = [
 "anyhow",
 "async-trait",
 "codec",
 "collections",
 "fail",
 "futures 0.3.15",
 "itertools",
 "kvproto",
 "log_wrappers",
 "match-template",
 "protobuf",
 "slog",
 "slog-global",
 "smallvec",
 "tidb_query_aggr",
 "tidb_query_codegen",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_expr",
 "tikv_util",
 "tipb",
 "tipb_helper",
 "yatp",
]

[[package]]
name = "tidb_query_expr"
version = "0.0.1"
dependencies = [
 "base64",
 "bstr",
 "byteorder",
 "chrono",
 "codec",
 "file_system",
 "flate2",
 "hex 0.4.2",
 "log_wrappers",
 "match-template",
 "num 0.3.0",
 "num-traits",
 "openssl",
 "panic_hook",
 "profiler",
 "protobuf",
 "rand 0.8.5",
 "regex",
 "safemem",
 "serde",
 "serde_json",
 "static_assertions",
 "tidb_query_codegen",
 "tidb_query_common",
 "tidb_query_datatype",
 "tikv_util",
 "time",
 "tipb",
 "tipb_helper",
 "twoway",
 "uuid 0.8.2",
]

[[package]]
name = "tikv"
version = "6.5.0"
dependencies = [
 "anyhow",
 "api_version",
 "async-stream 0.2.0",
 "async-trait",
 "backtrace",
 "batch-system",
 "byteorder",
 "bytes",
 "case_macros",
 "causal_ts",
 "chrono",
 "codec",
 "collections",
 "concurrency_manager",
 "coprocessor_plugin_api",
 "crc32fast",
 "crc64fast",
 "crossbeam",
 "dashmap",
 "encryption_export",
 "engine_panic",
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "engine_traits_tests",
 "error_code",
 "example_coprocessor_plugin",
 "fail",
 "file_system",
 "flate2",
 "futures 0.3.15",
 "futures-executor",
 "futures-timer",
 "futures-util",
 "fxhash",
 "getset",
 "grpcio",
 "grpcio-health",
 "hex 0.4.2",
 "http",
 "hyper",
 "hyper-openssl",
 "hyper-tls",
 "into_other",
 "itertools",
 "keyed_priority_queue",
 "keys",
 "kvproto",
 "lazy_static",
 "libc 0.2.132",
 "libloading",
 "log",
 "log_wrappers",
 "match-template",
 "memory_trace_macros",
 "mime",
 "more-asserts",
 "murmur3",
 "nom 5.1.0",
 "notify",
 "num-traits",
 "num_cpus",
 "online_config",
 "openssl",
 "panic_hook",
 "parking_lot 0.12.0",
 "paste",
 "pd_client",
 "pin-project",
 "pnet_datalink",
 "pprof",
 "procinfo",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "raft_log_engine",
 "raftstore",
 "rand 0.7.3",
 "regex",
 "reqwest",
 "resource_metering",
 "rev_lines",
 "seahash",
 "security",
 "semver 0.11.0",
 "serde",
 "serde_ignored",
 "serde_json",
 "slog",
 "slog-global",
 "smallvec",
 "sst_importer",
 "strum 0.20.0",
 "sync_wrapper",
 "sysinfo",
 "tempfile",
 "test_sst_importer",
 "test_util",
 "thiserror",
 "tidb_query_aggr",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_executors",
 "tidb_query_expr",
 "tikv_alloc",
 "tikv_kv",
 "tikv_util",
 "time",
 "tipb",
 "tokio",
 "tokio-openssl",
 "tokio-timer",
 "toml",
 "tracker",
 "txn_types",
 "url",
 "uuid 0.8.2",
 "walkdir",
 "yatp",
 "zipf",
]

[[package]]
name = "tikv-ctl"
version = "0.0.1"
dependencies = [
 "backup",
 "cc",
 "cdc",
 "chrono",
 "clap 2.33.0",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption_export",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "file_system",
 "futures 0.3.15",
 "gag",
 "grpcio",
 "hex 0.4.2",
 "keys",
 "kvproto",
 "libc 0.2.132",
 "log",
 "log_wrappers",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raft-engine-ctl",
 "raft_log_engine",
 "raftstore",
 "rand 0.8.5",
 "regex",
 "security",
 "serde_json",
 "server",
 "signal-hook",
 "slog",
 "slog-global",
 "structopt",
 "tempfile",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "time",
 "tokio",
 "toml",
 "txn_types",
]

[[package]]
name = "tikv-jemalloc-ctl"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e37706572f4b151dff7a0146e040804e9c26fe3a3118591112f05cf12a4216c1"
dependencies = [
 "libc 0.2.132",
 "paste",
 "tikv-jemalloc-sys",
]

[[package]]
name = "tikv-jemalloc-sys"
version = "0.5.0+5.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aeab4310214fe0226df8bfeb893a291a58b19682e8a07e1e1d4483ad4200d315"
dependencies = [
 "cc",
 "fs_extra",
 "libc 0.2.132",
]

[[package]]
name = "tikv-jemallocator"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20612db8a13a6c06d57ec83953694185a367e16945f66565e8028d2c0bd76979"
dependencies = [
 "libc 0.2.132",
 "tikv-jemalloc-sys",
]

[[package]]
name = "tikv-server"
version = "0.0.1"
dependencies = [
 "cc",
 "clap 2.33.0",
 "serde_json",
 "server",
 "tikv",
 "time",
 "toml",
]

[[package]]
name = "tikv_alloc"
version = "0.1.0"
dependencies = [
 "fxhash",
 "lazy_static",
 "libc 0.2.132",
 "mimalloc",
 "snmalloc-rs",
 "tcmalloc",
 "tempfile",
 "tikv-jemalloc-ctl",
 "tikv-jemalloc-sys",
 "tikv-jemallocator",
]

[[package]]
name = "tikv_kv"
version = "0.1.0"
dependencies = [
 "backtrace",
 "collections",
 "engine_panic",
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "error_code",
 "fail",
 "file_system",
 "futures 0.3.15",
 "into_other",
 "keys",
 "kvproto",
 "log_wrappers",
 "panic_hook",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "raft",
 "raftstore",
 "slog",
 "slog-global",
 "slog_derive",
 "tempfile",
 "thiserror",
 "tikv_util",
 "tracker",
 "txn_types",
]

[[package]]
name = "tikv_util"
version = "0.1.0"
dependencies = [
 "async-speed-limit",
 "backtrace",
 "byteorder",
 "bytes",
 "chrono",
 "codec",
 "collections",
 "cpu-time",
 "crc32fast",
 "crossbeam",
 "derive_more",
 "error_code",
 "fail",
 "futures 0.3.15",
 "futures-util",
 "gag",
 "grpcio",
 "http",
 "kvproto",
 "lazy_static",
 "libc 0.2.132",
 "log",
 "log_wrappers",
 "mnt",
 "nix 0.24.1",
 "num-traits",
 "num_cpus",
 "online_config",
 "openssl",
 "page_size",
 "panic_hook",
 "pin-project",
 "procfs",
 "procinfo",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "rand 0.8.5",
 "regex",
 "rusoto_core",
 "serde",
 "serde_json",
 "slog",
 "slog-async",
 "slog-global",
 "slog-json",
 "slog-term",
 "sysinfo",
 "tempfile",
 "thiserror",
 "tikv_alloc",
 "time",
 "tokio",
 "tokio-executor",
 "tokio-timer",
 "toml",
 "tracker",
 "url",
 "utime",
 "yatp",
]

[[package]]
name = "time"
version = "0.1.42"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db8dcfca086c1143c9270ac42a2bbd8a7ee477b78ac8e45b19abfb0cbede4b6f"
dependencies = [
 "libc 0.2.132",
 "redox_syscall 0.1.56",
 "winapi 0.3.9",
]

[[package]]
name = "tinytemplate"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2ada8616fad06a2d0c455adc530de4ef57605a8120cc65da9653e0e9623ca74"
dependencies = [
 "serde",
 "serde_json",
]

[[package]]
name = "tipb"
version = "0.0.1"
source = "git+https://github.com/pingcap/tipb.git#f3286471a05a4454a1071dd5f66ac7dbf6c79ba3"
dependencies = [
 "futures 0.3.15",
 "grpcio",
 "protobuf",
 "protobuf-build",
]

[[package]]
name = "tipb_helper"
version = "0.0.1"
dependencies = [
 "codec",
 "tidb_query_datatype",
 "tipb",
]

[[package]]
name = "tirocks"
version = "0.1.0"
source = "git+https://github.com/busyjay/tirocks.git?branch=dev#6cddeaeea1349bab891e2c0913e0ebb1a755e697"
dependencies = [
 "libc 0.2.132",
 "paste",
 "tirocks-sys",
]

[[package]]
name = "tirocks-sys"
version = "0.1.0"
source = "git+https://github.com/busyjay/tirocks.git?branch=dev#6cddeaeea1349bab891e2c0913e0ebb1a755e697"
dependencies = [
 "bzip2-sys",
 "cc",
 "cmake",
 "libc 0.2.132",
 "libz-sys",
 "lz4-sys",
 "snappy-sys",
 "zstd-sys",
]

[[package]]
name = "tokio"
version = "1.21.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9e03c497dc955702ba729190dc4aac6f2a0ce97f913e5b1b5912fc5039d9099"
dependencies = [
 "autocfg",
 "bytes",
 "libc 0.2.132",
 "memchr",
 "mio 0.8.5",
 "num_cpus",
 "parking_lot 0.12.0",
 "pin-project-lite",
 "signal-hook-registry",
 "socket2",
 "tokio-macros",
 "winapi 0.3.9",
]

[[package]]
name = "tokio-executor"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb2d1b8f4548dbf5e1f7818512e9c406860678f29c300cdf0ebac72d1a3a1671"
dependencies = [
 "crossbeam-utils 0.7.2",
 "futures 0.1.31",
]

[[package]]
name = "tokio-io-timeout"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30b74022ada614a1b4834de765f9bb43877f910cc8ce4be40e89042c9223a8bf"
dependencies = [
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-macros"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b557f72f448c511a979e2564e55d74e6c4432fc96ff4f6241bc6bded342643b7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "tokio-native-tls"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7d995660bd2b7f8c1568414c1126076c13fbb725c40112dc0120b78eb9b717b"
dependencies = [
 "native-tls",
 "tokio",
]

[[package]]
name = "tokio-openssl"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac1bec5c0a4aa71e3459802c7a12e8912c2091ce2151004f9ce95cc5d1c6124e"
dependencies = [
 "futures 0.3.15",
 "openssl",
 "pin-project",
 "tokio",
]

[[package]]
name = "tokio-stream"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d660770404473ccd7bc9f8b28494a811bc18542b915c0855c51e8f419d5223ce"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-timer"
version = "0.2.13"
source = "git+https://github.com/tikv/tokio?branch=tokio-timer-hotfix#e8ac149d93f4a9bf49ea569d8d313ee40c5eb448"
dependencies = [
 "crossbeam-utils 0.7.2",
 "futures 0.1.31",
 "slab",
 "tokio-executor",
]

[[package]]
name = "tokio-util"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f988a1a1adc2fb21f9c12aa96441da33a1728193ae0b95d2be22dbd17fcb4e5c"
dependencies = [
 "bytes",
 "futures-core",
 "futures-io",
 "futures-sink",
 "pin-project-lite",
 "tokio",
 "tracing",
]

[[package]]
name = "toml"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75cf45bb0bef80604d001caaec0d09da99611b3c0fd39d3080468875cdb65645"
dependencies = [
 "serde",
]

[[package]]
name = "tonic"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55b9af819e54b8f33d453655bef9b9acc171568fb49523078d0cc4e7484200ec"
dependencies = [
 "async-stream 0.3.3",
 "async-trait",
 "axum",
 "base64",
 "bytes",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "hyper",
 "hyper-timeout",
 "percent-encoding",
 "pin-project",
 "prost",
 "prost-derive",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tower",
 "tower-layer",
 "tower-service",
 "tracing",
 "tracing-futures",
]

[[package]]
name = "tonic-build"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c6fd7c2581e36d63388a9e04c350c21beb7a8b059580b2e93993c526899ddc"
dependencies = [
 "prettyplease",
 "proc-macro2",
 "prost-build",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "tower"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fa9be0de6cf49e536ce1851f987bd21a43b771b09473c3549a6c853db37c1c"
dependencies = [
 "futures-core",
 "futures-util",
 "indexmap",
 "pin-project",
 "pin-project-lite",
 "rand 0.8.5",
 "slab",
 "tokio",
 "tokio-util",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower-http"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c530c8675c1dbf98facee631536fa116b5fb6382d7dd6dc1b118d970eafe3ba"
dependencies = [
 "bitflags",
 "bytes",
 "futures-core",
 "futures-util",
 "http",
 "http-body",
 "http-range-header",
 "pin-project-lite",
 "tower",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-layer"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "343bc9466d3fe6b0f960ef45960509f84480bf4fd96f92901afe7ff3df9d3a62"

[[package]]
name = "tower-service"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6bc1c9ce2b5135ac7f93c72918fc37feb872bdc6a5533a8b85eb4b86bfdae52"

[[package]]
name = "tracing"
version = "0.1.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01ebdc2bb4498ab1ab5f5b73c5803825e60199229ccba0698170e3be0e7f959f"
dependencies = [
 "cfg-if 1.0.0",
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc6b8ad3567499f98a1db7a752b07a7c8c7c7c34c332ec00effb2b0027974b7c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "tracing-core"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f50de3927f93d202783f4513cda820ab47ef17f624b03c096e86ef00c67e6b5f"
dependencies = [
 "lazy_static",
]

[[package]]
name = "tracing-futures"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97d095ae15e245a057c8e8451bab9b3ee1e1f68e9ba2b4fbc18d0ac5237835f2"
dependencies = [
 "pin-project",
 "tracing",
]

[[package]]
name = "tracker"
version = "0.0.1"
dependencies = [
 "collections",
 "crossbeam-utils 0.8.8",
 "kvproto",
 "lazy_static",
 "parking_lot 0.12.0",
 "pin-project",
 "prometheus",
 "slab",
]

[[package]]
name = "try-lock"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e604eb7b43c06650e854be16a2a03155743d3752dd1c943f6829e26b7a36e382"

[[package]]
name = "twoway"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "766345ed3891b291d01af307cd3ad2992a4261cb6c0c7e665cd3e01cf379dd24"
dependencies = [
 "memchr",
 "unchecked-index",
]

[[package]]
name = "txn_types"
version = "0.1.0"
dependencies = [
 "bitflags",
 "byteorder",
 "codec",
 "collections",
 "error_code",
 "farmhash",
 "kvproto",
 "log_wrappers",
 "panic_hook",
 "rand 0.8.5",
 "slog",
 "thiserror",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "typenum"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "373c8a200f9e67a0c95e62a4f52fbf80c23b4381c05a17845531982fa99e6b33"

[[package]]
name = "ucd-trie"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56dee185309b50d1f11bfedef0fe6d036842e3fb77413abef29f8f8d1c5d4c1c"

[[package]]
name = "unchecked-index"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eeba86d422ce181a719445e51872fa30f1f7413b62becb52e95ec91aa262d85c"

[[package]]
name = "unicode-bidi"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49f2bd0c6468a8230e1db229cff8029217cf623c767ea5d60bfbd42729ea54d5"
dependencies = [
 "matches",
]

[[package]]
name = "unicode-ident"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ceab39d59e4c9499d4e5a8ee0e2735b891bb7308ac83dfb4e80cad195c9f6f3"

[[package]]
name = "unicode-normalization"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5479532badd04e128284890390c1e876ef7a993d0570b3597ae43dfa1d59afa4"
dependencies = [
 "smallvec",
]

[[package]]
name = "unicode-segmentation"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1967f4cdfc355b37fd76d2a954fb2ed3871034eb4f26d60537d88795cfc332a9"

[[package]]
name = "unicode-width"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7007dbd421b92cc6e28410fe7362e2e0a2503394908f417b68ec8d1c364c4e20"

[[package]]
name = "url"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a507c383b2d33b5fc35d1861e77e6b383d158b2da5e14fe51b83dfedf6fd578c"
dependencies = [
 "form_urlencoded",
 "idna",
 "matches",
 "percent-encoding",
]

[[package]]
name = "utime"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "055058552ca15c566082fc61da433ae678f78986a6f16957e33162d1b218792a"
dependencies = [
 "kernel32-sys",
 "libc 0.2.132",
 "winapi 0.2.8",
]

[[package]]
name = "uuid"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc5cf98d8186244414c848017f0e2676b3fcb46807f6668a97dfe67359a3c4b7"
dependencies = [
 "getrandom 0.2.3",
 "serde",
]

[[package]]
name = "uuid"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "feb41e78f93363bb2df8b0e86a2ca30eed7806ea16ea0c790d757cf93f79be83"

[[package]]
name = "valgrind_request"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0fb139b14473e1350e34439c888e44c805f37b4293d17f87ea920a66a20a99a"

[[package]]
name = "vcpkg"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426"

[[package]]
name = "vec_map"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05c78687fb1a80548ae3250346c3db86a80a7cdd77bda190189f2d0a0987c81a"

[[package]]
name = "version_check"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "914b1a6776c4c929a602fafd8bc742e06365d4bcbe48c30f9cca5824f70dc9dd"

[[package]]
name = "version_check"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49874b5167b65d7193b8aba1567f5c7d93d001cafc34600cee003eda787e483f"

[[package]]
name = "visible"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a044005fd5c0fc1ebd79c622e5606431c6b879a6a19acafb754be9926a2de73e"
dependencies = [
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "walkdir"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "777182bc735b6424e1a57516d35ed72cb8019d85c8c9bf536dccb3445c1a2f7d"
dependencies = [
 "same-file",
 "winapi 0.3.9",
 "winapi-util",
]

[[package]]
name = "want"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ce8a968cb1cd110d136ff8b819a556d6fb6d919363c61534f6860c7eb172ba0"
dependencies = [
 "log",
 "try-lock",
]

[[package]]
name = "wasi"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b89c3ce4ce14bdc6fb6beaf9ec7928ca331de5df7e5ea278375642a2f478570d"

[[package]]
name = "wasi"
version = "0.10.2+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd6fbd9a79829dd1ad0cc20627bf1ed606756a7f77edff7b66b7064f9cb327c6"

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasm-bindgen"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25f1af7423d8588a3d840681122e72e6a24ddbcb3f0ec385cac0d12d24256c06"
dependencies = [
 "cfg-if 1.0.0",
 "serde",
 "serde_json",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b21c0df030f5a177f3cba22e9bc4322695ec43e7257d865302900290bcdedca"
dependencies = [
 "bumpalo",
 "lazy_static",
 "log",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3de431a2910c86679c34283a33f66f4e4abd7e0aec27b6669060148872aadf94"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f4203d69e40a52ee523b2529a773d5ffc1dc0071801c87b3d270b471b80ed01"
dependencies = [
 "quote",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa8a30d46208db204854cadbb5d4baf5fcf8071ba5bf48190c3e59937962ebc"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d958d035c4438e28c70e4321a2911302f10135ce78a9c7834c0cab4123d06a2"

[[package]]
name = "web-sys"
version = "0.3.56"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c060b319f29dd25724f09a2ba1418f142f539b2be99fbf4d2d5a8f7330afb8eb"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "which"
version = "4.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a5a7e487e921cf220206864a94a89b6c6905bfc19f1057fa26a4cb360e5c1d2"
dependencies = [
 "either",
 "lazy_static",
 "libc 0.2.132",
]

[[package]]
name = "winapi"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "167dc9d6949a9b857f3451275e911c3f44255842c1f7a76f33c55103a909087a"

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-build"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d315eee3b34aca4797b2da6b13ed88266e6d612562a0c46390af8299fc699bc"

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70ec6ce85bb158151cae5e5c87f95a8e97d2c0c4b001223f33a334e3ce5de178"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "windows-sys"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3df6e476185f92a12c072be4a189a0210dcdcf512a1891d6dff9edb874deadc6"
dependencies = [
 "windows_aarch64_msvc 0.32.0",
 "windows_i686_gnu 0.32.0",
 "windows_i686_msvc 0.32.0",
 "windows_x86_64_gnu 0.32.0",
 "windows_x86_64_msvc 0.32.0",
]

[[package]]
name = "windows-sys"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a3e1820f08b8513f676f7ab6c1f99ff312fb97b553d30ff4dd86f9f15728aa7"
dependencies = [
 "windows_aarch64_gnullvm",
 "windows_aarch64_msvc 0.42.0",
 "windows_i686_gnu 0.42.0",
 "windows_i686_msvc 0.42.0",
 "windows_x86_64_gnu 0.42.0",
 "windows_x86_64_gnullvm",
 "windows_x86_64_msvc 0.42.0",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41d2aa71f6f0cbe00ae5167d90ef3cfe66527d6f613ca78ac8024c3ccab9a19e"

[[package]]
name = "windows_aarch64_msvc"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8e92753b1c443191654ec532f14c199742964a061be25d77d7a96f09db20bf5"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd0f252f5a35cac83d6311b2e795981f5ee6e67eb1f9a7f64eb4500fbc4dcdb4"

[[package]]
name = "windows_i686_gnu"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a711c68811799e017b6038e0922cb27a5e2f43a2ddb609fe0b6f3eeda9de615"

[[package]]
name = "windows_i686_gnu"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbeae19f6716841636c28d695375df17562ca208b2b7d0dc47635a50ae6c5de7"

[[package]]
name = "windows_i686_msvc"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "146c11bb1a02615db74680b32a68e2d61f553cc24c4eb5b4ca10311740e44172"

[[package]]
name = "windows_i686_msvc"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84c12f65daa39dd2babe6e442988fc329d6243fdce47d7d2d155b8d874862246"

[[package]]
name = "windows_x86_64_gnu"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c912b12f7454c6620635bbff3450962753834be2a594819bd5e945af18ec64bc"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf7b1b21b5362cbc318f686150e5bcea75ecedc74dd157d874d754a2ca44b0ed"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09d525d2ba30eeb3297665bd434a54297e4170c7f1a44cad4ef58095b4cd2028"

[[package]]
name = "windows_x86_64_msvc"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "504a2476202769977a040c6364301a3f65d0cc9e3fb08600b2bda150a0488316"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40009d85759725a34da6d89a94e63d7bdc50a862acf0dbc7c8e488f1edcb6f5"

[[package]]
name = "winreg"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0120db82e8a1e0b9fb3345a539c478767c0048d842860994d96113d5b667bd69"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "ws2_32-sys"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d59cefebd0c892fa2dd6de581e937301d8552cb44489cdff035c6187cb63fa5e"
dependencies = [
 "winapi 0.2.8",
 "winapi-build",
]

[[package]]
name = "x86"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "637be4bacc6c06570eb05a3ba513f81d63e52862ced82db542215dd48dbab1e5"
dependencies = [
 "bit_field",
 "bitflags",
 "csv",
 "phf",
 "phf_codegen",
 "raw-cpuid",
 "serde_json",
]

[[package]]
name = "xdg"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d089681aa106a86fade1b0128fb5daf07d5867a509ab036d99988dec80429a57"

[[package]]
name = "xml-rs"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "541b12c998c5b56aa2b4e6f18f03664eef9a4fd0a246a55594efae6cc2d964b5"

[[package]]
name = "yatp"
version = "0.0.1"
source = "git+https://github.com/tikv/yatp.git?branch=master#39cb495953d40a7e846363c06090755c2eac65fa"
dependencies = [
 "crossbeam-deque",
 "dashmap",
 "fail",
 "lazy_static",
 "num_cpus",
 "parking_lot_core 0.9.1",
 "prometheus",
 "rand 0.8.5",
]

[[package]]
name = "zeroize"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3cbac2ed2ba24cc90f5e06485ac8c7c1e5449fe8911aef4d8877218af021a5b8"

[[package]]
name = "zipf"
version = "6.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e12b8667a4fff63d236f8363be54392f93dbb13616be64a83e761a9319ab589"
dependencies = [
 "rand 0.7.3",
]

[[package]]
name = "zstd"
version = "0.11.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20cc960326ece64f010d2d2107537f26dc589a6573a316bd5b1dba685fa5fde4"
dependencies = [
 "zstd-safe",
]

[[package]]
name = "zstd-safe"
version = "5.0.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d2a5585e04f9eea4b2a3d1eca508c4dee9592a89ef6f450c11719da0726f4db"
dependencies = [
 "libc 0.2.132",
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.1+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fd07cbbc53846d9145dbffdf6dd09a7a0aa52be46741825f5c97bdd4f73f12b"
dependencies = [
 "cc",
 "libc 0.2.132",
]

[[patch.unused]]
name = "rusoto_kms"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=gh1482-s3-addr-styles#0d6df7b119c4e757daaa715f261c3150c7ae0a3b"

[[patch.unused]]
name = "rusoto_mock"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=gh1482-s3-addr-styles#0d6df7b119c4e757daaa715f261c3150c7ae0a3b"

[[patch.unused]]
name = "rusoto_s3"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=gh1482-s3-addr-styles#0d6df7b119c4e757daaa715f261c3150c7ae0a3b"

[[patch.unused]]
name = "rusoto_sts"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=gh1482-s3-addr-styles#0d6df7b119c4e757daaa715f261c3150c7ae0a3b"
