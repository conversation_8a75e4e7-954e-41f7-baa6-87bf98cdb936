[package]
name = "sst_importer"
version = "0.1.0"
edition = "2018"
publish = false

[features]
cloud-storage-grpc = ["external_storage_export/cloud-storage-grpc"]
cloud-storage-dylib = ["external_storage_export/cloud-storage-dylib"]

[dependencies]
api_version = { workspace = true }
crc32fast = "1.2"
dashmap = "5"
encryption = { workspace = true }
engine_rocks = { workspace = true }
engine_traits = { workspace = true }
error_code = { workspace = true }
external_storage_export = { workspace = true }
file_system = { workspace = true }
futures = { version = "0.3", features = ["thread-pool"] }
futures-util = { version = "0.3", default-features = false, features = ["io"] }
grpcio = { workspace = true }
keys = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
log_wrappers = { workspace = true }
openssl = "0.10"
prometheus = { version = "0.13", default-features = false }
rand = "0.8"
serde = { version = "1.0.210", features = ["derive"] }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
thiserror = "1.0"
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.5", features = ["time", "rt-multi-thread", "macros"] }
txn_types = { workspace = true }
uuid = { version = "0.8.1", features = ["serde", "v4"] }

[dev-dependencies]
tempfile = "3.0"
test_sst_importer = { workspace = true }
test_util = { workspace = true }
