[package]
name = "tidb_query_datatype"
version = "0.0.1"
edition = "2018"
publish = false
description = "Data type of a query engine to run TiDB pushed down executors"

[dependencies]
base64 = "0.13"
bitfield = "0.13.2"
bitflags = "1.0.1"
boolinator = "2.4.0"
bstr = "0.2.8"
chrono = "0.4"
chrono-tz = "0.5.1"
codec = { workspace = true }
collections = { workspace = true }
encoding_rs = { git = "https://github.com/xiongjiwei/encoding_rs.git", rev = "68e0bc5a72a37a78228d80cd98047326559cf43c" }
error_code = { workspace = true }
hex = "0.4"
kvproto = { workspace = true }
lazy_static = "1.3"
log_wrappers = { workspace = true }
match-template = "0.0.1"
nom = { version = "7.1.0", default-features = false, features = ["std"] }
num = { version = "0.3", default-features = false }
num-derive = "0.3"
num-traits = "0.2"
ordered-float = "2.0"
protobuf = "2"
regex = "1.1"
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
static_assertions = { version = "1.0", features = ["nightly"] }
thiserror = "1.0"
tidb_query_common = { workspace = true }
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
tipb = { workspace = true }
