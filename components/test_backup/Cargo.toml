[package]
name = "test_backup"
version = "0.0.1"
edition = "2018"
publish = false

[dependencies]
api_version = { workspace = true }
backup = { workspace = true }
collections = { workspace = true }
concurrency_manager = { workspace = true }
crc64fast = "0.1"
engine_traits = { workspace = true }
external_storage_export = { workspace = true }
file_system = { workspace = true }
futures = "0.3"
futures-executor = "0.3"
futures-util = { version = "0.3", default-features = false, features = ["io"] }
grpcio = { workspace = true }
kvproto = { workspace = true }
protobuf = "2"
rand = "0.8"
tempfile = "3.0"
test_raftstore = { workspace = true }
tidb_query_common = { workspace = true }
tikv = { workspace = true }
tikv_util = { workspace = true }
txn_types = { workspace = true }
