[package]
name = "pd_client"
version = "0.1.0"
edition = "2018"
publish = false

[features]
failpoints = ["fail/failpoints"]
testexport = []

[dependencies]
collections = { workspace = true }
error_code = { workspace = true }
fail = "0.5"
futures = "0.3"
grpcio = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
log = { version = "0.4", features = ["max_level_trace", "release_max_level_debug"] }
log_wrappers = { workspace = true }
prometheus = { version = "0.13", features = ["nightly"] }
security = { workspace = true }
semver = "0.10"
serde = { version = "1.0.210", features = ["derive"] }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
thiserror = "1.0"
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1", features = ["sync"] }
tokio-timer = { workspace = true }
txn_types = { workspace = true }
yatp = { workspace = true }
