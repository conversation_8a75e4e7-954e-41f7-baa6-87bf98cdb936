[package]
name = "resource_metering"
version = "0.0.1"
edition = "2018"

[dependencies]
collections = { workspace = true }
crossbeam = "0.8"
futures = "0.3"
grpcio = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
libc = "0.2"
log = { version = "0.4", features = ["max_level_trace", "release_max_level_debug"] }
online_config = { workspace = true }
pdqselect = "0.1"
pin-project = "1.0"
prometheus = { version = "0.13", features = ["nightly"] }
serde = { version = "1.0.210", features = ["derive"] }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tikv_util = { workspace = true }

[target.'cfg(target_os = "linux")'.dependencies]
procinfo = { git = "https://github.com/tikv/procinfo-rs", rev = "6599eb9dca74229b2c1fcc44118bef7eff127128" }

[dev-dependencies]
rand = "0.8"

[[test]]
name = "test-recorder"
path = "tests/recorder_test.rs"
