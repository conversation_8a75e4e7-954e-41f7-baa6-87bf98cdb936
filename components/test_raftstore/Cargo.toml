[package]
name = "test_raftstore"
version = "0.0.1"
edition = "2018"
publish = false

[features]
default = ["test-engine-kv-rocksdb", "test-engine-raft-raft-engine"]
test-engine-kv-rocksdb = [
  "raftstore/test-engine-kv-rocksdb"
]
test-engine-raft-raft-engine = [
  "raftstore/test-engine-raft-raft-engine"
]
test-engines-rocksdb = [
  "raftstore/test-engines-rocksdb",
]
test-engines-panic = [
  "raftstore/test-engines-panic",
]

[dependencies]
api_version = { workspace = true }
backtrace = "0.3"
causal_ts = { workspace = true, features = ["testexport"] }
collections = { workspace = true }
concurrency_manager = { workspace = true }
crossbeam = "0.8"
encryption_export = { workspace = true }
engine_rocks = { workspace = true }
engine_rocks_helper = { workspace = true }
engine_test = { workspace = true }
engine_traits = { workspace = true }
fail = "0.5"
file_system = { workspace = true }
futures = "0.3"
grpcio = { workspace = true }
grpcio-health = { version = "0.10", default-features = false, features = ["protobuf-codec"] }
keys = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
log_wrappers = { workspace = true }
pd_client = { workspace = true }
protobuf = { version = "2.8", features = ["bytes"] }
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raftstore = { workspace = true, features = ["testexport"] }
rand = "0.8"
resolved_ts = { workspace = true }
resource_metering = { workspace = true }
security = { workspace = true }
server = { workspace = true }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
# better to not use slog-global, but pass in the logger
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tempfile = "3.0"
test_pd_client = { workspace = true }
test_util = { workspace = true }
tikv = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.5", features = ["rt-multi-thread"] }
tokio-timer = { workspace = true }
txn_types = { workspace = true }
